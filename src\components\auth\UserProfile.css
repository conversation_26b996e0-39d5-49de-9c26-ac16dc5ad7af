/* User Profile Styles */
.user-profile {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Profile Header */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.profile-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  backdrop-filter: blur(10px);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.profile-email {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 0.75rem 0;
}

.profile-role-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.profile-actions {
  display: flex;
  gap: 1rem;
}

.edit-actions {
  display: flex;
  gap: 1rem;
}

/* Buttons */
.btn-primary {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Profile Content */
.profile-content {
  display: flex;
  min-height: 500px;
}

/* Tabs */
.profile-tabs {
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 1rem 0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  color: #495057;
}

.tab-button:hover {
  background: #e9ecef;
}

.tab-active {
  background: #3498db;
  color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

/* Tab Content */
.tab-content {
  flex: 1;
  padding: 2rem;
}

.tab-content h2 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

/* Profile Form */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.form-group-full {
  grid-column: 1 / -1;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3498db;
}

.form-input:disabled,
.form-textarea:disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Permissions Tab */
.permissions-tab {
  max-width: 600px;
}

.permissions-description {
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.permissions-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.permission-icon {
  font-size: 1.2rem;
}

.permission-name {
  font-weight: 500;
  color: #495057;
}

/* Security Tab */
.security-tab {
  max-width: 600px;
}

.security-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.security-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.security-section h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.security-description {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.security-tab .btn-secondary {
  background: #6c757d;
  color: white;
  border: 1px solid #6c757d;
}

.security-tab .btn-secondary:hover {
  background: #5a6268;
  border-color: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .profile-content {
    flex-direction: column;
  }

  .profile-tabs {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 0;
  }

  .tab-button {
    white-space: nowrap;
    min-width: 150px;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .profile-name {
    font-size: 1.5rem;
  }
}
