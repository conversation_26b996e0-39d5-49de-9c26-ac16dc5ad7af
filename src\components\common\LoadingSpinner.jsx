import React from 'react';
import './LoadingSpinner.css';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  text = '', 
  overlay = false,
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'spinner-sm',
    md: 'spinner-md',
    lg: 'spinner-lg',
    xl: 'spinner-xl'
  };

  const colorClasses = {
    primary: 'spinner-primary',
    secondary: 'spinner-secondary',
    success: 'spinner-success',
    warning: 'spinner-warning',
    error: 'spinner-error',
    white: 'spinner-white'
  };

  const spinnerContent = (
    <div className={`loading-spinner-container ${className}`}>
      <div className={`loading-spinner ${sizeClasses[size]} ${colorClasses[color]}`}>
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
      </div>
      {text && <p className="loading-text">{text}</p>}
    </div>
  );

  if (overlay) {
    return (
      <div className="loading-overlay">
        {spinnerContent}
      </div>
    );
  }

  return spinnerContent;
};

// Skeleton Loader Component
export const SkeletonLoader = ({ 
  width = '100%', 
  height = '1rem', 
  borderRadius = 'var(--radius-md)',
  className = '' 
}) => {
  return (
    <div 
      className={`skeleton ${className}`}
      style={{ 
        width, 
        height, 
        borderRadius 
      }}
    />
  );
};

// Pulse Animation Component
export const PulseLoader = ({ children, loading = false }) => {
  return (
    <div className={`pulse-container ${loading ? 'pulse-loading' : ''}`}>
      {children}
    </div>
  );
};

// Dots Loader Component
export const DotsLoader = ({ color = 'primary', size = 'md' }) => {
  const sizeClasses = {
    sm: 'dots-sm',
    md: 'dots-md',
    lg: 'dots-lg'
  };

  const colorClasses = {
    primary: 'dots-primary',
    secondary: 'dots-secondary',
    white: 'dots-white'
  };

  return (
    <div className={`dots-loader ${sizeClasses[size]} ${colorClasses[color]}`}>
      <div className="dot"></div>
      <div className="dot"></div>
      <div className="dot"></div>
    </div>
  );
};

// Progress Bar Component
export const ProgressBar = ({ 
  progress = 0, 
  color = 'primary', 
  size = 'md',
  showPercentage = false,
  animated = true 
}) => {
  const sizeClasses = {
    sm: 'progress-sm',
    md: 'progress-md',
    lg: 'progress-lg'
  };

  const colorClasses = {
    primary: 'progress-primary',
    secondary: 'progress-secondary',
    success: 'progress-success',
    warning: 'progress-warning',
    error: 'progress-error'
  };

  return (
    <div className="progress-container">
      <div className={`progress-bar ${sizeClasses[size]}`}>
        <div 
          className={`progress-fill ${colorClasses[color]} ${animated ? 'progress-animated' : ''}`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <span className="progress-percentage">{Math.round(progress)}%</span>
      )}
    </div>
  );
};

export default LoadingSpinner;
