import React, { useState, useEffect } from 'react';
import appointmentService, { APPOINTMENT_STATUS } from '../../services/appointmentService';
import './AppointmentCalendar.css';

const AppointmentCalendar = ({ onDateSelect, onAppointmentSelect, selectedDate }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState([]);
  const [viewMode, setViewMode] = useState('month'); // 'month', 'week', 'day'

  useEffect(() => {
    loadAppointments();
  }, [currentDate, viewMode]);

  const loadAppointments = () => {
    let startDate, endDate;
    
    if (viewMode === 'month') {
      startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    } else if (viewMode === 'week') {
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      startDate = startOfWeek;
      endDate = new Date(startOfWeek);
      endDate.setDate(startOfWeek.getDate() + 6);
    } else {
      startDate = new Date(currentDate);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(currentDate);
      endDate.setHours(23, 59, 59, 999);
    }
    
    const appointmentsInRange = appointmentService.getAppointmentsByDateRange(startDate, endDate);
    setAppointments(appointmentsInRange);
  };

  const navigateDate = (direction) => {
    const newDate = new Date(currentDate);
    
    if (viewMode === 'month') {
      newDate.setMonth(currentDate.getMonth() + direction);
    } else if (viewMode === 'week') {
      newDate.setDate(currentDate.getDate() + (direction * 7));
    } else {
      newDate.setDate(currentDate.getDate() + direction);
    }
    
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getAppointmentsForDate = (date) => {
    const dateStr = date.toDateString();
    return appointments.filter(appointment => {
      return new Date(appointment.date).toDateString() === dateStr;
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case APPOINTMENT_STATUS.SCHEDULED:
        return '#3498db';
      case APPOINTMENT_STATUS.CONFIRMED:
        return '#2ecc71';
      case APPOINTMENT_STATUS.IN_PROGRESS:
        return '#f39c12';
      case APPOINTMENT_STATUS.COMPLETED:
        return '#27ae60';
      case APPOINTMENT_STATUS.CANCELLED:
        return '#e74c3c';
      case APPOINTMENT_STATUS.NO_SHOW:
        return '#95a5a6';
      default:
        return '#bdc3c7';
    }
  };

  const renderMonthView = () => {
    const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const currentDateObj = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      const dayAppointments = getAppointmentsForDate(currentDateObj);
      const isCurrentMonth = currentDateObj.getMonth() === currentDate.getMonth();
      const isToday = currentDateObj.toDateString() === new Date().toDateString();
      const isSelected = selectedDate && currentDateObj.toDateString() === selectedDate.toDateString();
      
      days.push(
        <div
          key={i}
          className={`calendar-day ${!isCurrentMonth ? 'other-month' : ''} ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}`}
          onClick={() => onDateSelect && onDateSelect(new Date(currentDateObj))}
        >
          <div className="day-number">{currentDateObj.getDate()}</div>
          <div className="day-appointments">
            {dayAppointments.slice(0, 3).map((appointment, index) => (
              <div
                key={appointment.id}
                className="appointment-dot"
                style={{ backgroundColor: getStatusColor(appointment.status) }}
                title={`${formatTime(appointment.date)} - ${appointment.customerName} (${appointment.serviceName})`}
                onClick={(e) => {
                  e.stopPropagation();
                  onAppointmentSelect && onAppointmentSelect(appointment);
                }}
              />
            ))}
            {dayAppointments.length > 3 && (
              <div className="more-appointments">+{dayAppointments.length - 3}</div>
            )}
          </div>
        </div>
      );
      
      currentDateObj.setDate(currentDateObj.getDate() + 1);
    }
    
    return (
      <div className="calendar-grid">
        <div className="calendar-header">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="calendar-header-day">{day}</div>
          ))}
        </div>
        <div className="calendar-body">
          {days}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      const dayAppointments = getAppointmentsForDate(day);
      
      days.push(
        <div key={i} className="week-day">
          <div className="week-day-header">
            <div className="week-day-name">{day.toLocaleDateString('en-US', { weekday: 'short' })}</div>
            <div className="week-day-number">{day.getDate()}</div>
          </div>
          <div className="week-day-appointments">
            {dayAppointments.map(appointment => (
              <div
                key={appointment.id}
                className="week-appointment"
                style={{ borderLeft: `4px solid ${getStatusColor(appointment.status)}` }}
                onClick={() => onAppointmentSelect && onAppointmentSelect(appointment)}
              >
                <div className="appointment-time">{formatTime(appointment.date)}</div>
                <div className="appointment-customer">{appointment.customerName}</div>
                <div className="appointment-service">{appointment.serviceName}</div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    return <div className="week-view">{days}</div>;
  };

  const renderDayView = () => {
    const dayAppointments = getAppointmentsForDate(currentDate);
    const hours = [];
    
    for (let hour = 8; hour < 20; hour++) {
      const hourAppointments = dayAppointments.filter(appointment => {
        return new Date(appointment.date).getHours() === hour;
      });
      
      hours.push(
        <div key={hour} className="day-hour">
          <div className="hour-label">
            {hour === 12 ? '12 PM' : hour > 12 ? `${hour - 12} PM` : `${hour} AM`}
          </div>
          <div className="hour-appointments">
            {hourAppointments.map(appointment => (
              <div
                key={appointment.id}
                className="day-appointment"
                style={{ backgroundColor: getStatusColor(appointment.status) }}
                onClick={() => onAppointmentSelect && onAppointmentSelect(appointment)}
              >
                <div className="appointment-time">{formatTime(appointment.date)}</div>
                <div className="appointment-details">
                  <div className="appointment-customer">{appointment.customerName}</div>
                  <div className="appointment-service">{appointment.serviceName}</div>
                  <div className="appointment-staff">{appointment.staffName}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    
    return <div className="day-view">{hours}</div>;
  };

  const getViewTitle = () => {
    if (viewMode === 'month') {
      return currentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    } else if (viewMode === 'week') {
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
    } else {
      return formatDate(currentDate);
    }
  };

  return (
    <div className="appointment-calendar">
      <div className="calendar-controls">
        <div className="calendar-navigation">
          <button onClick={() => navigateDate(-1)} className="nav-btn">
            &#8249;
          </button>
          <h2 className="calendar-title">{getViewTitle()}</h2>
          <button onClick={() => navigateDate(1)} className="nav-btn">
            &#8250;
          </button>
        </div>
        
        <div className="calendar-actions">
          <button onClick={goToToday} className="btn btn-secondary">
            Today
          </button>
          <div className="view-mode-selector">
            <button
              onClick={() => setViewMode('month')}
              className={`view-btn ${viewMode === 'month' ? 'active' : ''}`}
            >
              Month
            </button>
            <button
              onClick={() => setViewMode('week')}
              className={`view-btn ${viewMode === 'week' ? 'active' : ''}`}
            >
              Week
            </button>
            <button
              onClick={() => setViewMode('day')}
              className={`view-btn ${viewMode === 'day' ? 'active' : ''}`}
            >
              Day
            </button>
          </div>
        </div>
      </div>
      
      <div className="calendar-content">
        {viewMode === 'month' && renderMonthView()}
        {viewMode === 'week' && renderWeekView()}
        {viewMode === 'day' && renderDayView()}
      </div>
      
      <div className="calendar-legend">
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: getStatusColor(APPOINTMENT_STATUS.SCHEDULED) }}></div>
          <span>Scheduled</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: getStatusColor(APPOINTMENT_STATUS.CONFIRMED) }}></div>
          <span>Confirmed</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: getStatusColor(APPOINTMENT_STATUS.IN_PROGRESS) }}></div>
          <span>In Progress</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: getStatusColor(APPOINTMENT_STATUS.COMPLETED) }}></div>
          <span>Completed</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: getStatusColor(APPOINTMENT_STATUS.CANCELLED) }}></div>
          <span>Cancelled</span>
        </div>
      </div>
    </div>
  );
};

export default AppointmentCalendar;
