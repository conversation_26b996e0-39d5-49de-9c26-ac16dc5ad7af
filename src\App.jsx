import './App.css'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { EnhancedToaster } from './components/common/Toast'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/auth/ProtectedRoute'
import Header from './components/Header/header'
import Footer from './components/Footer/Footer'
import Home from './components/Home/Home'
import About from './components/About/About'
import ContactUs from './components/ContactUs/ContactUs'
import SignIn from './components/SignIn/SignIn'
import DashboardLayout from './components/dashboard/DashboardLayout'
import DashboardHome from './components/dashboard/DashboardHome'
import UserProfile from './components/auth/UserProfile'
import CustomerManagement from './components/customers/CustomerManagement'
import AppointmentManagement from './components/appointments/AppointmentManagement'
import ServiceManagement from './components/services/ServiceManagement'
import StaffManagement from './components/staff/StaffManagement'

// Placeholder components for dashboard routes
const BillingPage = () => <div className="page-placeholder">Billing Management - Coming Soon</div>
const InventoryPage = () => <div className="page-placeholder">Inventory Management - Coming Soon</div>
const ReportsPage = () => <div className="page-placeholder">Reports & Analytics - Coming Soon</div>

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="app">
          {/* Enhanced Toast notifications */}
          <EnhancedToaster />

          <Routes>
            {/* Public Routes */}
            <Route path="/" element={
              <>
                <Header />
                <Home />
                <Footer />
              </>
            } />
            <Route path="/about" element={
              <>
                <Header />
                <About />
                <Footer />
              </>
            } />
            <Route path="/contact" element={
              <>
                <Header />
                <ContactUs />
                <Footer />
              </>
            } />
            <Route path="/signin" element={
              <>
                <Header />
                <SignIn />
                <Footer />
              </>
            } />

            {/* Protected Dashboard Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              {/* Dashboard Home */}
              <Route index element={<DashboardHome />} />

              {/* Customer Management */}
              <Route path="customers" element={
                <ProtectedRoute requiredRoles={['admin', 'staff', 'receptionist']}>
                  <CustomerManagement />
                </ProtectedRoute>
              } />

              {/* Appointment Management */}
              <Route path="appointments" element={
                <ProtectedRoute requiredRoles={['admin', 'staff', 'receptionist']}>
                  <AppointmentManagement />
                </ProtectedRoute>
              } />

              {/* Services Management */}
              <Route path="services" element={
                <ProtectedRoute requiredRoles={['admin', 'staff']}>
                  <ServiceManagement />
                </ProtectedRoute>
              } />

              {/* Staff Management - Admin Only */}
              <Route path="staff" element={
                <ProtectedRoute requiredRole="admin">
                  <StaffManagement />
                </ProtectedRoute>
              } />

              {/* Billing Management */}
              <Route path="billing" element={
                <ProtectedRoute requiredRoles={['admin', 'receptionist']}>
                  <BillingPage />
                </ProtectedRoute>
              } />

              {/* Inventory Management */}
              <Route path="inventory" element={
                <ProtectedRoute requiredRoles={['admin', 'staff']}>
                  <InventoryPage />
                </ProtectedRoute>
              } />

              {/* Reports - Admin Only */}
              <Route path="reports" element={
                <ProtectedRoute requiredRole="admin">
                  <ReportsPage />
                </ProtectedRoute>
              } />

              {/* Profile Settings */}
              <Route path="profile" element={
                <ProtectedRoute>
                  <UserProfile />
                </ProtectedRoute>
              } />
            </Route>

            {/* Catch all route - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
