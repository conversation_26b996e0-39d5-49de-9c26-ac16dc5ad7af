import React, { useState, useMemo } from 'react';
import serviceService, { SERVICE_CATEGORIES, SERVICE_STATUS } from '../../services/serviceService';
import './ServiceCatalog.css';

const ServiceCatalog = ({ services, onEdit, onDelete, canEdit }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');


  const categories = serviceService.getCategories();

  // Filter and sort services
  const filteredAndSortedServices = useMemo(() => {
    let filtered = services;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(service => service.category === selectedCategory);
    }

    // Sort services
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'name' || sortBy === 'description') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [services, searchTerm, selectedCategory, sortBy, sortOrder]);

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`;
    }
    return `${mins}m`;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      [SERVICE_STATUS.ACTIVE]: { label: 'Active', className: 'status-active' },
      [SERVICE_STATUS.INACTIVE]: { label: 'Inactive', className: 'status-inactive' },
      [SERVICE_STATUS.DISCONTINUED]: { label: 'Discontinued', className: 'status-discontinued' }
    };

    const config = statusConfig[status] || statusConfig[SERVICE_STATUS.ACTIVE];
    return <span className={`status-badge ${config.className}`}>{config.label}</span>;
  };

  const getPopularityStars = (popularity) => {
    const stars = Math.round(popularity / 20); // Convert to 1-5 scale
    return '⭐'.repeat(stars) + '☆'.repeat(5 - stars);
  };

  return (
    <div className="service-catalog">
      <div className="services-header">
        <div className="header-content">
          <h2 className="section-title">
            <span className="title-icon">🛍️</span>
            Service Catalog
          </h2>
          <p className="section-subtitle">
            Browse and manage your salon services
          </p>
        </div>

        {canEdit && (
          <button
            className="btn btn-primary"
            onClick={() => onEdit(null)}
          >
            <span className="btn-icon">➕</span>
            Add Service
          </button>
        )}
      </div>

      {/* Services Display */}
      {filteredAndSortedServices.length === 0 ? (
        <div className="no-services">
          <div className="no-services-icon">🔍</div>
          <h3>No services found</h3>
          <p>Try adjusting your search or filter criteria</p>
        </div>
      ) : (
        <div className="services-grid">
          {filteredAndSortedServices.map((service) => (
            <div key={service.id} className="service-card">
              <div className="service-header">
                <h3 className="service-name">{service.name}</h3>
                {getStatusBadge(service.status)}
              </div>

              <p className="service-description">{service.description}</p>

              <div className="service-details">
                <div className="detail-item">
                  <span className="detail-label">Category:</span>
                  <span className="detail-value">
                    {categories[service.category]?.icon || '🛍️'} {categories[service.category]?.name || service.category}
                  </span>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Price:</span>
                  <span className="detail-value">${service.price}</span>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Duration:</span>
                  <span className="detail-value">{formatDuration(service.duration)}</span>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Popularity:</span>
                  <span className="detail-value">{getPopularityStars(service.popularity)}</span>
                </div>
              </div>

              {service.requirements && service.requirements.length > 0 && (
                <div className="service-requirements">
                  <h4>Requirements:</h4>
                  <ul className="requirements-list">
                    {service.requirements.map((requirement, index) => (
                      <li key={index} className="requirement-item">
                        {requirement}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {canEdit && (
                <div className="service-actions">
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={() => onEdit(service)}
                  >
                    <span className="btn-icon">✏️</span>
                    Edit
                  </button>
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => onDelete(service.id)}
                  >
                    <span className="btn-icon">🗑️</span>
                    Delete
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ServiceCatalog;
