/* Appointment Reminders Styles */
.appointment-reminders {
  padding: 2rem;
}

/* Header */
.reminders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.reminders-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.reminder-count {
  background: #007bff;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Reminder Settings */
.reminder-settings {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #007bff;
}

.setting-item {
  margin-bottom: 1rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-weight: 500;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.setting-item select {
  margin-left: 0.5rem;
  padding: 0.375rem 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.setting-item select:focus {
  outline: none;
  border-color: #007bff;
}

.setting-item select:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Reminders List */
.reminders-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.no-reminders {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.no-reminders p {
  margin: 0;
  font-size: 1.1rem;
}

/* Reminder Items */
.reminder-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reminder-item:last-child {
  border-bottom: none;
}

.reminder-item:hover {
  background: #f8f9fa;
  transform: translateX(4px);
}

.reminder-item.urgent {
  border-left: 4px solid #dc3545;
  background: #fff5f5;
}

.reminder-item.warning {
  border-left: 4px solid #ffc107;
  background: #fffbf0;
}

.reminder-item.info {
  border-left: 4px solid #007bff;
  background: #f8f9ff;
}

.reminder-item.normal {
  border-left: 4px solid #28a745;
  background: #f8fff8;
}

.reminder-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  gap: 1rem;
}

.reminder-main {
  flex: 1;
}

.customer-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.appointment-details {
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-size: 0.95rem;
}

.appointment-time {
  color: #007bff;
  font-weight: 500;
  font-size: 0.9rem;
}

.reminder-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: right;
}

.time-until {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
}

.urgency-indicator {
  font-size: 1.2rem;
}

/* Reminder Actions */
.reminder-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 1.1rem;
}

.action-btn:hover {
  background: #f8f9fa;
}

.send-reminder:hover {
  background: #e3f2fd;
}

.dismiss:hover {
  background: #ffebee;
}

/* Footer */
.reminders-footer {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .appointment-reminders {
    padding: 1rem;
  }
  
  .reminders-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .reminders-header h3 {
    font-size: 1.25rem;
  }
  
  .reminder-settings {
    padding: 1rem;
  }
  
  .reminder-item {
    padding: 1rem;
  }
  
  .reminder-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .reminder-meta {
    align-self: flex-end;
  }
  
  .reminder-actions {
    margin-left: 0;
    margin-top: 0.5rem;
  }
  
  .reminders-footer {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .appointment-reminders {
    padding: 0.5rem;
  }
  
  .reminders-header h3 {
    font-size: 1.1rem;
  }
  
  .reminder-count {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .reminder-settings {
    padding: 0.75rem;
  }
  
  .setting-item label {
    font-size: 0.9rem;
  }
  
  .reminder-item {
    padding: 0.75rem;
  }
  
  .customer-name {
    font-size: 1rem;
  }
  
  .appointment-details {
    font-size: 0.85rem;
  }
  
  .appointment-time {
    font-size: 0.8rem;
  }
  
  .time-until {
    font-size: 0.85rem;
  }
  
  .urgency-indicator {
    font-size: 1rem;
  }
  
  .action-btn {
    padding: 0.375rem;
    font-size: 1rem;
  }
  
  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .no-reminders {
    padding: 2rem 1rem;
  }
  
  .no-reminders p {
    font-size: 1rem;
  }
}
