/* Enhanced Form Components Styles */

/* Form Field Base */
.form-field {
  margin-bottom: var(--space-6);
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  transition: color var(--transition-base);
}

.form-label.required::after {
  content: ' *';
  color: var(--color-error-500);
}

.form-error {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--color-error-600);
  animation: slideInUp 0.3s ease-out;
}

.form-error::before {
  content: '⚠️';
  font-size: var(--font-size-xs);
}

.form-helper {
  display: block;
  margin-top: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* Enhanced Input Styles */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  overflow: hidden;
}

.input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-base);
  z-index: 0;
}

.input-wrapper.focused::before {
  opacity: 0.1;
}

.input-wrapper.focused {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.input-wrapper.error {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-wrapper.disabled {
  background: var(--color-gray-50);
  border-color: var(--color-gray-200);
  cursor: not-allowed;
}

.form-input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: none;
  background: transparent;
  font-size: var(--font-size-base);
  color: var(--color-gray-900);
  outline: none;
  position: relative;
  z-index: 1;
}

.form-input::placeholder {
  color: var(--color-gray-400);
  transition: color var(--transition-base);
}

.input-wrapper.focused .form-input::placeholder {
  color: var(--color-gray-300);
}

.form-input:disabled {
  color: var(--color-gray-500);
  cursor: not-allowed;
}

/* Input Sizes */
.input-wrapper.sm {
  min-height: 36px;
}

.input-wrapper.sm .form-input {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
}

.input-wrapper.md {
  min-height: 44px;
}

.input-wrapper.lg {
  min-height: 52px;
}

.input-wrapper.lg .form-input {
  padding: var(--space-4) var(--space-5);
  font-size: var(--font-size-lg);
}

/* Input Icons */
.input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-400);
  font-size: var(--font-size-base);
  transition: color var(--transition-base);
  z-index: 1;
}

.input-icon.left {
  padding-left: var(--space-4);
  padding-right: var(--space-2);
}

.input-icon.right {
  padding-left: var(--space-2);
  padding-right: var(--space-4);
}

.password-toggle {
  background: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
}

.password-toggle:hover {
  color: var(--color-gray-600);
  transform: scale(1.1);
}

.input-wrapper.focused .input-icon {
  color: var(--color-primary-500);
}

/* Enhanced Select Styles */
.select-wrapper {
  position: relative;
  background: white;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.select-wrapper.open,
.select-wrapper:focus-within {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.select-wrapper.error {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.select-wrapper.disabled {
  background: var(--color-gray-50);
  border-color: var(--color-gray-200);
  cursor: not-allowed;
}

.select-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--color-gray-900);
  text-align: left;
}

.select-trigger:disabled {
  cursor: not-allowed;
  color: var(--color-gray-500);
}

.select-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-arrow {
  margin-left: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--color-gray-400);
  transition: transform var(--transition-base);
}

.select-arrow.open {
  transform: rotate(180deg);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid var(--color-primary-500);
  border-top: none;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-dropdown);
  animation: slideInDown 0.2s ease-out;
  max-height: 300px;
  overflow: hidden;
}

.select-search {
  padding: var(--space-3);
  border-bottom: 1px solid var(--color-gray-200);
}

.search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  outline: none;
}

.search-input:focus {
  border-color: var(--color-primary-500);
}

.select-options {
  max-height: 200px;
  overflow-y: auto;
}

.select-option {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: all var(--transition-base);
  border-bottom: 1px solid var(--color-gray-100);
}

.select-option:hover {
  background: var(--color-gray-50);
}

.select-option.selected {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
  font-weight: var(--font-weight-medium);
}

.select-option.disabled {
  color: var(--color-gray-400);
  cursor: not-allowed;
}

.option-checkbox {
  margin: 0;
}

.option-label {
  flex: 1;
}

/* Enhanced Textarea Styles */
.textarea-wrapper {
  position: relative;
  background: white;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.textarea-wrapper.focused {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.textarea-wrapper.error {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.textarea-wrapper.disabled {
  background: var(--color-gray-50);
  border-color: var(--color-gray-200);
}

.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: none;
  background: transparent;
  font-size: var(--font-size-base);
  color: var(--color-gray-900);
  outline: none;
  resize: vertical;
  font-family: inherit;
  line-height: var(--line-height-relaxed);
}

.form-textarea::placeholder {
  color: var(--color-gray-400);
}

.form-textarea:disabled {
  color: var(--color-gray-500);
  cursor: not-allowed;
  resize: none;
}

.character-count {
  position: absolute;
  bottom: var(--space-2);
  right: var(--space-3);
  font-size: var(--font-size-xs);
  color: var(--color-gray-400);
  background: white;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
}

/* Enhanced Checkbox Styles */
.checkbox-field {
  margin-bottom: var(--space-4);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-base);
}

.checkbox-label.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-base);
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.checkbox-custom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.checkbox-input:checked + .checkbox-custom {
  border-color: var(--color-primary-500);
}

.checkbox-input:checked + .checkbox-custom::before {
  opacity: 1;
}

.checkbox-check {
  color: white;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  position: relative;
  z-index: 1;
  animation: checkBounce 0.3s ease-out;
}

.checkbox-text {
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  user-select: none;
}

.checkbox-label:hover .checkbox-custom {
  border-color: var(--color-primary-400);
  transform: scale(1.05);
}

.checkbox-field.error .checkbox-custom {
  border-color: var(--color-error-500);
}

/* Checkbox Sizes */
.checkbox-label.sm .checkbox-custom {
  width: 16px;
  height: 16px;
}

.checkbox-label.sm .checkbox-text {
  font-size: var(--font-size-sm);
}

.checkbox-label.lg .checkbox-custom {
  width: 24px;
  height: 24px;
}

.checkbox-label.lg .checkbox-text {
  font-size: var(--font-size-lg);
}

/* Form Layout Components */
.form-group {
  margin-bottom: var(--space-8);
}

.form-row {
  display: grid;
  align-items: end;
}

.form-row.gap-sm {
  gap: var(--space-3);
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.form-row.gap-md {
  gap: var(--space-4);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.form-row.gap-lg {
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes checkBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .input-wrapper,
[data-theme="dark"] .select-wrapper,
[data-theme="dark"] .textarea-wrapper {
  background: var(--color-gray-800);
  border-color: var(--color-gray-600);
}

[data-theme="dark"] .form-input,
[data-theme="dark"] .form-textarea,
[data-theme="dark"] .select-trigger {
  color: var(--color-gray-100);
}

[data-theme="dark"] .form-input::placeholder,
[data-theme="dark"] .form-textarea::placeholder {
  color: var(--color-gray-500);
}

[data-theme="dark"] .select-dropdown {
  background: var(--color-gray-800);
  border-color: var(--color-primary-500);
}

[data-theme="dark"] .select-option:hover {
  background: var(--color-gray-700);
}

[data-theme="dark"] .checkbox-custom {
  background: var(--color-gray-800);
  border-color: var(--color-gray-600);
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-row.gap-sm,
  .form-row.gap-md,
  .form-row.gap-lg {
    gap: var(--space-4);
  }
  
  .input-wrapper,
  .select-wrapper {
    min-height: 48px;
  }
  
  .form-input,
  .select-trigger {
    font-size: var(--font-size-base);
  }
}
