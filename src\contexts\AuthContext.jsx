import React, { createContext, useContext, useReducer, useEffect } from 'react';
import toast from 'react-hot-toast';

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  STAFF: 'staff',
  RECEPTIONIST: 'receptionist'
};

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      };
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Demo users for frontend-only implementation
const DEMO_USERS = [
  {
    id: 1,
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: USER_ROLES.ADMIN,
    avatar: null
  },
  {
    id: 2,
    email: '<EMAIL>',
    password: 'staff123',
    name: 'Staff Member',
    role: USER_ROLES.STAFF,
    avatar: null
  },
  {
    id: 3,
    email: '<EMAIL>',
    password: 'reception123',
    name: 'Receptionist',
    role: USER_ROLES.RECEPTIONIST,
    avatar: null
  }
];

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const token = localStorage.getItem('salon_auth_token');
        const userData = localStorage.getItem('salon_user_data');
        
        if (token && userData) {
          const user = JSON.parse(userData);
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: user
          });
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkAuthStatus();
  }, []);

  // Login function
  const login = async (email, password, rememberMe = false) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user in demo users
      const user = DEMO_USERS.find(u => u.email === email && u.password === password);

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Create user session data (excluding password)
      const userSession = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        avatar: user.avatar
      };

      // Generate a simple token (in real app, this would come from backend)
      const token = btoa(JSON.stringify({ userId: user.id, timestamp: Date.now() }));

      // Store in localStorage
      localStorage.setItem('salon_auth_token', token);
      localStorage.setItem('salon_user_data', JSON.stringify(userSession));
      
      if (rememberMe) {
        localStorage.setItem('salon_remember_me', 'true');
      }

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: userSession
      });

      toast.success(`Welcome back, ${user.name}!`);
      return { success: true, user: userSession };

    } catch (error) {
      const errorMessage = error.message || 'Login failed. Please try again.';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage
      });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('salon_auth_token');
    localStorage.removeItem('salon_user_data');
    localStorage.removeItem('salon_remember_me');
    
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
    toast.success('Logged out successfully');
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return state.user?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(state.user?.role);
  };

  // Check if user is admin
  const isAdmin = () => hasRole(USER_ROLES.ADMIN);

  // Check if user is staff or admin
  const isStaffOrAdmin = () => hasAnyRole([USER_ROLES.STAFF, USER_ROLES.ADMIN]);

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  const value = {
    ...state,
    login,
    logout,
    hasRole,
    hasAnyRole,
    isAdmin,
    isStaffOrAdmin,
    clearError,
    USER_ROLES
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
