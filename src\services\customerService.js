// Customer Management Service
// This service provides mock data and operations for customer management
// In a real application, this would interact with a backend API

class CustomerService {
  constructor() {
    this.STORAGE_KEY = 'salon_customers';
    this.customers = this.loadCustomers();
    this.nextId = this.getNextId();
  }

  // Load customers from localStorage or generate mock data
  loadCustomers() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
    }
    
    // Generate initial mock data
    const mockCustomers = this.generateMockCustomers();
    this.saveCustomers(mockCustomers);
    return mockCustomers;
  }

  // Save customers to localStorage
  saveCustomers(customers = this.customers) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(customers));
      this.customers = customers;
    } catch (error) {
      console.error('Error saving customers:', error);
    }
  }

  // Get next available ID
  getNextId() {
    const maxId = this.customers.reduce((max, customer) => 
      Math.max(max, parseInt(customer.id) || 0), 0);
    return maxId + 1;
  }

  // Generate mock customer data
  generateMockCustomers() {
    const firstNames = ['John', '<PERSON>', 'Michael', '<PERSON>', '<PERSON>', 'Emily', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    const lastNames = ['<PERSON>', '<PERSON>', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'];
    const services = ['Haircut', 'Hair Color', 'Highlights', 'Perm', 'Facial', 'Manicure', 'Pedicure', 'Massage', 'Eyebrow Threading', 'Hair Styling'];
    
    const customers = [];
    
    for (let i = 1; i <= 25; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`;
      const phone = `(${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;
      
      // Generate random appointment history
      const appointmentCount = Math.floor(Math.random() * 10) + 1;
      const history = [];
      
      for (let j = 0; j < appointmentCount; j++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 365));
        
        history.push({
          id: `hist_${i}_${j}`,
          date: date.toISOString(),
          service: services[Math.floor(Math.random() * services.length)],
          staff: `Staff ${Math.floor(Math.random() * 5) + 1}`,
          amount: Math.floor(Math.random() * 150) + 30,
          notes: j === 0 ? 'Regular customer, prefers morning appointments' : ''
        });
      }
      
      // Sort history by date (newest first)
      history.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      customers.push({
        id: i.toString(),
        firstName,
        lastName,
        email,
        phone,
        dateOfBirth: this.generateRandomDate(new Date(1950, 0, 1), new Date(2005, 11, 31)),
        address: {
          street: `${Math.floor(Math.random() * 9999) + 1} ${['Main', 'Oak', 'Pine', 'Elm', 'Cedar'][Math.floor(Math.random() * 5)]} St`,
          city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][Math.floor(Math.random() * 5)],
          state: ['NY', 'CA', 'IL', 'TX', 'AZ'][Math.floor(Math.random() * 5)],
          zipCode: Math.floor(Math.random() * 90000) + 10000
        },
        preferences: {
          preferredServices: services.slice(0, Math.floor(Math.random() * 3) + 1),
          preferredStaff: [`Staff ${Math.floor(Math.random() * 5) + 1}`],
          allergies: Math.random() > 0.7 ? ['Chemical dyes', 'Fragrances'][Math.floor(Math.random() * 2)] : '',
          notes: Math.random() > 0.5 ? 'Prefers natural products' : ''
        },
        history,
        totalVisits: appointmentCount,
        totalSpent: history.reduce((sum, visit) => sum + visit.amount, 0),
        lastVisit: history[0]?.date || null,
        status: Math.random() > 0.1 ? 'active' : 'inactive',
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    return customers;
  }

  // Generate random date between two dates
  generateRandomDate(start, end) {
    const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  // Get all customers
  getAllCustomers() {
    return [...this.customers];
  }

  // Get customer by ID
  getCustomerById(id) {
    return this.customers.find(customer => customer.id === id.toString());
  }

  // Search and filter customers
  searchCustomers(query = '', filters = {}) {
    let filteredCustomers = [...this.customers];
    
    // Text search
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.firstName.toLowerCase().includes(searchTerm) ||
        customer.lastName.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm) ||
        customer.phone.includes(searchTerm)
      );
    }
    
    // Status filter
    if (filters.status && filters.status !== 'all') {
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.status === filters.status
      );
    }
    
    // Date range filter (last visit)
    if (filters.dateFrom || filters.dateTo) {
      filteredCustomers = filteredCustomers.filter(customer => {
        if (!customer.lastVisit) return false;
        
        const lastVisitDate = new Date(customer.lastVisit);
        const fromDate = filters.dateFrom ? new Date(filters.dateFrom) : new Date(0);
        const toDate = filters.dateTo ? new Date(filters.dateTo) : new Date();
        
        return lastVisitDate >= fromDate && lastVisitDate <= toDate;
      });
    }
    
    // Sort customers
    const sortBy = filters.sortBy || 'lastName';
    const sortOrder = filters.sortOrder || 'asc';
    
    filteredCustomers.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'firstName':
          aValue = a.firstName.toLowerCase();
          bValue = b.firstName.toLowerCase();
          break;
        case 'lastName':
          aValue = a.lastName.toLowerCase();
          bValue = b.lastName.toLowerCase();
          break;
        case 'email':
          aValue = a.email.toLowerCase();
          bValue = b.email.toLowerCase();
          break;
        case 'lastVisit':
          aValue = a.lastVisit ? new Date(a.lastVisit) : new Date(0);
          bValue = b.lastVisit ? new Date(b.lastVisit) : new Date(0);
          break;
        case 'totalSpent':
          aValue = a.totalSpent;
          bValue = b.totalSpent;
          break;
        case 'totalVisits':
          aValue = a.totalVisits;
          bValue = b.totalVisits;
          break;
        default:
          aValue = a.lastName.toLowerCase();
          bValue = b.lastName.toLowerCase();
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
    
    return filteredCustomers;
  }

  // Add new customer
  addCustomer(customerData) {
    try {
      const newCustomer = {
        id: this.nextId.toString(),
        firstName: customerData.firstName || '',
        lastName: customerData.lastName || '',
        email: customerData.email || '',
        phone: customerData.phone || '',
        dateOfBirth: customerData.dateOfBirth || '',
        address: {
          street: customerData.address?.street || '',
          city: customerData.address?.city || '',
          state: customerData.address?.state || '',
          zipCode: customerData.address?.zipCode || ''
        },
        preferences: {
          preferredServices: customerData.preferences?.preferredServices || [],
          preferredStaff: customerData.preferences?.preferredStaff || [],
          allergies: customerData.preferences?.allergies || '',
          notes: customerData.preferences?.notes || ''
        },
        history: [],
        totalVisits: 0,
        totalSpent: 0,
        lastVisit: null,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.customers.push(newCustomer);
      this.nextId++;
      this.saveCustomers();

      return { success: true, customer: newCustomer };
    } catch (error) {
      console.error('Error adding customer:', error);
      return { success: false, error: error.message };
    }
  }

  // Update existing customer
  updateCustomer(id, customerData) {
    try {
      const customerIndex = this.customers.findIndex(customer => customer.id === id.toString());

      if (customerIndex === -1) {
        return { success: false, error: 'Customer not found' };
      }

      const existingCustomer = this.customers[customerIndex];

      const updatedCustomer = {
        ...existingCustomer,
        firstName: customerData.firstName || existingCustomer.firstName,
        lastName: customerData.lastName || existingCustomer.lastName,
        email: customerData.email || existingCustomer.email,
        phone: customerData.phone || existingCustomer.phone,
        dateOfBirth: customerData.dateOfBirth || existingCustomer.dateOfBirth,
        address: {
          ...existingCustomer.address,
          ...customerData.address
        },
        preferences: {
          ...existingCustomer.preferences,
          ...customerData.preferences
        },
        status: customerData.status || existingCustomer.status,
        updatedAt: new Date().toISOString()
      };

      this.customers[customerIndex] = updatedCustomer;
      this.saveCustomers();

      return { success: true, customer: updatedCustomer };
    } catch (error) {
      console.error('Error updating customer:', error);
      return { success: false, error: error.message };
    }
  }

  // Delete customer
  deleteCustomer(id) {
    try {
      const customerIndex = this.customers.findIndex(customer => customer.id === id.toString());

      if (customerIndex === -1) {
        return { success: false, error: 'Customer not found' };
      }

      const deletedCustomer = this.customers.splice(customerIndex, 1)[0];
      this.saveCustomers();

      return { success: true, customer: deletedCustomer };
    } catch (error) {
      console.error('Error deleting customer:', error);
      return { success: false, error: error.message };
    }
  }

  // Add appointment to customer history
  addAppointmentToHistory(customerId, appointmentData) {
    try {
      const customer = this.getCustomerById(customerId);

      if (!customer) {
        return { success: false, error: 'Customer not found' };
      }

      const newAppointment = {
        id: `hist_${customerId}_${Date.now()}`,
        date: appointmentData.date || new Date().toISOString(),
        service: appointmentData.service || '',
        staff: appointmentData.staff || '',
        amount: appointmentData.amount || 0,
        notes: appointmentData.notes || ''
      };

      customer.history.unshift(newAppointment); // Add to beginning
      customer.totalVisits = customer.history.length;
      customer.totalSpent = customer.history.reduce((sum, visit) => sum + visit.amount, 0);
      customer.lastVisit = newAppointment.date;
      customer.updatedAt = new Date().toISOString();

      this.saveCustomers();

      return { success: true, appointment: newAppointment };
    } catch (error) {
      console.error('Error adding appointment to history:', error);
      return { success: false, error: error.message };
    }
  }

  // Get customer statistics
  getCustomerStats() {
    const totalCustomers = this.customers.length;
    const activeCustomers = this.customers.filter(c => c.status === 'active').length;
    const inactiveCustomers = totalCustomers - activeCustomers;

    const totalRevenue = this.customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
    const averageSpending = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    const recentCustomers = this.customers.filter(customer => {
      const createdDate = new Date(customer.createdAt);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return createdDate >= thirtyDaysAgo;
    }).length;

    return {
      totalCustomers,
      activeCustomers,
      inactiveCustomers,
      totalRevenue,
      averageSpending,
      recentCustomers
    };
  }

  // Validate customer data
  validateCustomer(customerData) {
    const errors = {};

    if (!customerData.firstName?.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!customerData.lastName?.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!customerData.email?.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!customerData.phone?.trim()) {
      errors.phone = 'Phone number is required';
    } else if (!/^[\d\s\-\(\)]+$/.test(customerData.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Check if email already exists (for new customers)
  isEmailExists(email, excludeId = null) {
    return this.customers.some(customer =>
      customer.email.toLowerCase() === email.toLowerCase() &&
      customer.id !== excludeId
    );
  }

  // Get customers with pagination
  getCustomersPaginated(page = 1, limit = 10, query = '', filters = {}) {
    const allFilteredCustomers = this.searchCustomers(query, filters);
    const totalCustomers = allFilteredCustomers.length;
    const totalPages = Math.ceil(totalCustomers / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const customers = allFilteredCustomers.slice(startIndex, endIndex);

    return {
      customers,
      pagination: {
        currentPage: page,
        totalPages,
        totalCustomers,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
        limit
      }
    };
  }
}

// Create and export a singleton instance
const customerService = new CustomerService();
export default customerService;
