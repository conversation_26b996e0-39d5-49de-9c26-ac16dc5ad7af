import React from 'react';
import './StaffPerformance.css';

const StaffPerformance = ({ staff, onBack, canEdit }) => {
  return (
    <div className="staff-performance">
      <div className="performance-header">
        <div className="header-content">
          <button
            className="btn btn-secondary"
            onClick={onBack}
          >
            <span className="btn-icon">←</span>
            Back to Staff List
          </button>
          
          <div className="header-info">
            <h1 className="page-title">
              <span className="title-icon">📊</span>
              Performance - {staff.firstName} {staff.lastName}
            </h1>
            <p className="page-subtitle">
              Track performance metrics, commissions, and analytics
            </p>
          </div>
        </div>
      </div>

      <div className="performance-content">
        <div className="coming-soon">
          <div className="coming-soon-icon">📊</div>
          <h3>Staff Performance Analytics</h3>
          <p>This feature is coming soon! You'll be able to:</p>
          <ul>
            <li>View detailed performance metrics and KPIs</li>
            <li>Track commission earnings and sales targets</li>
            <li>Analyze customer satisfaction ratings</li>
            <li>Monitor appointment completion rates</li>
            <li>Set and track performance goals</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default StaffPerformance;
