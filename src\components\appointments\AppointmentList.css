/* Appointment List Styles */
.appointment-list {
  padding: 2rem;
}

/* List Controls */
.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-bar {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
  color: #adb5bd;
}

.filters {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 0.95rem;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #007bff;
}

/* Table Container */
.appointments-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Table Styles */
.appointments-table {
  width: 100%;
  border-collapse: collapse;
}

.appointments-table thead {
  background: #f8f9fa;
}

.appointments-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  position: relative;
}

.appointments-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease;
}

.appointments-table th.sortable:hover {
  background: #e9ecef;
}

.appointments-table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: top;
}

.appointment-row {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.appointment-row:hover {
  background: #f8f9fa;
}

/* Table Cell Content */
.date-time {
  min-width: 120px;
}

.date {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.time {
  color: #007bff;
  font-size: 0.9rem;
}

.customer {
  min-width: 150px;
}

.customer-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.customer-phone {
  color: #6c757d;
  font-size: 0.9rem;
}

.service {
  min-width: 140px;
}

.service-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.service-duration {
  color: #6c757d;
  font-size: 0.9rem;
}

.staff {
  min-width: 120px;
  font-weight: 500;
  color: #495057;
}

.status {
  min-width: 120px;
}

.status-select {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.status-select:hover {
  opacity: 0.9;
}

.price {
  min-width: 80px;
  font-weight: 600;
  color: #28a745;
  font-size: 1.1rem;
}

.actions {
  min-width: 100px;
  text-align: center;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 1.1rem;
  margin: 0 0.25rem;
}

.action-btn:hover {
  background: #f8f9fa;
}

.edit-btn:hover {
  background: #e3f2fd;
}

.delete-btn:hover {
  background: #ffebee;
}

/* No Appointments State */
.no-appointments {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
  font-style: italic;
}

/* List Summary */
.list-summary {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .appointments-table-container {
    overflow-x: auto;
  }
  
  .appointments-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .appointment-list {
    padding: 1rem;
  }
  
  .list-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-bar {
    min-width: auto;
  }
  
  .filters {
    justify-content: center;
  }
  
  .appointments-table th,
  .appointments-table td {
    padding: 0.75rem 0.5rem;
  }
  
  .appointments-table th {
    font-size: 0.9rem;
  }
  
  .date-time,
  .customer,
  .service,
  .staff,
  .status,
  .actions {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .appointment-list {
    padding: 0.5rem;
  }
  
  .list-controls {
    gap: 0.75rem;
  }
  
  .search-input,
  .filter-select {
    padding: 0.625rem;
    font-size: 0.9rem;
  }
  
  .appointments-table {
    min-width: 600px;
  }
  
  .appointments-table th,
  .appointments-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .appointments-table th {
    font-size: 0.85rem;
  }
  
  .date,
  .customer-name,
  .service-name {
    font-size: 0.9rem;
  }
  
  .time,
  .customer-phone,
  .service-duration {
    font-size: 0.8rem;
  }
  
  .staff {
    font-size: 0.9rem;
  }
  
  .status-select {
    padding: 0.375rem;
    font-size: 0.8rem;
  }
  
  .price {
    font-size: 1rem;
  }
  
  .action-btn {
    padding: 0.375rem;
    font-size: 1rem;
    margin: 0 0.125rem;
  }
  
  .no-appointments {
    padding: 2rem 1rem;
    font-size: 0.9rem;
  }
  
  .list-summary {
    padding: 0.75rem;
    font-size: 0.85rem;
  }
}
