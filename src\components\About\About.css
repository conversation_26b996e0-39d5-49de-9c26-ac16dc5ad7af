/* Modern Hero Section - Same as Home */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-top: 80px;
}

.hero-content {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.hero-text {
  color: white;
  max-width: 600px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(47, 126, 180, 0.50);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2.5rem;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-accent-100));
  color: var(--color-primary-700);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: 1rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Story Section */
.story-section {
  padding: 6rem 2rem;
  background: var(--color-gray-50);
}

.story-container {
  max-width: 1400px;
  margin: 0 auto;
}

.story-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.story-text {
  max-width: 800px;
}

.story-paragraphs {
  margin-top: 2rem;
}

.story-paragraph {
  font-size: var(--font-size-base);
  line-height: 1.8;
  color: var(--color-gray-700);
  margin-bottom: 1.5rem;
}

.story-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.stat-card {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-gray-100);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--color-primary-600);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: 500;
}

/* Values Section */
.values-section {
  padding: 6rem 2rem;
  background: white;
}

.values-container {
  max-width: 1400px;
  margin: 0 auto;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.value-card {
  background: var(--color-gray-50);
  padding: 2.5rem 2rem;
  border-radius: 20px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.value-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  background: white;
}

.value-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
}

.value-icon svg {
  width: 32px;
  height: 32px;
}

.value-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.value-description {
  color: var(--color-gray-600);
  line-height: 1.6;
}

/* Team Section */
.team-section {
  padding: 6rem 2rem;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
}

.team-container {
  max-width: 1400px;
  margin: 0 auto;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.team-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.team-image {
  margin-bottom: 1.5rem;
}

.team-avatar {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  font-size: 2rem;
  font-weight: 700;
}

.team-name {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.team-role {
  font-size: var(--font-size-base);
  color: var(--color-primary-600);
  font-weight: 500;
  margin-bottom: 1rem;
}

.team-description {
  color: var(--color-gray-600);
  line-height: 1.6;
  font-size: var(--font-size-sm);
}

/* Mission Section */
.mission-section {
  padding: 6rem 2rem;
  background: white;
}

.mission-container {
  max-width: 1000px;
  margin: 0 auto;
}

.mission-content {
  text-align: center;
}

.mission-text {
  margin-top: 2rem;
}

.mission-paragraph {
  font-size: var(--font-size-lg);
  line-height: 1.8;
  color: var(--color-gray-700);
  margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .story-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .story-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .values-grid,
  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-section {
    margin-top: 70px;
  }

  .hero-content {
    padding: 1.5rem;
    gap: 2rem;
  }

  .hero-title {
    font-size: clamp(2rem, 5vw, 2.5rem);
  }

  .story-section,
  .values-section,
  .team-section,
  .mission-section {
    padding: 4rem 1.5rem;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .story-stats {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .values-grid,
  .team-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .value-card,
  .team-card {
    padding: 2rem 1.5rem;
  }

  .value-icon,
  .team-avatar {
    width: 70px;
    height: 70px;
  }

  .value-icon svg {
    width: 28px;
    height: 28px;
  }

  .team-avatar {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 1rem;
  }

  .hero-badge {
    font-size: var(--font-size-xs);
    padding: 0.375rem 0.75rem;
  }

  .story-section,
  .values-section,
  .team-section,
  .mission-section {
    padding: 3rem 1rem;
  }

  .story-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .value-card,
  .team-card {
    padding: 1.5rem;
  }

  .mission-container {
    padding: 0 1rem;
  }
}