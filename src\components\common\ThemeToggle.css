/* Theme Toggle Styles */
.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
  position: relative;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

.theme-toggle-track {
  width: 60px;
  height: 32px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  position: relative;
  transition: all var(--transition-base);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .theme-toggle-track {
  background: var(--color-gray-700);
}

.theme-toggle-thumb {
  width: 28px;
  height: 28px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateX(0);
}

[data-theme="dark"] .theme-toggle-thumb {
  transform: translateX(28px);
  background: var(--color-gray-800);
}

.theme-icon {
  font-size: 14px;
  transition: all var(--transition-base);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* Animation for theme transition */
.theme-toggle-thumb {
  animation: theme-bounce 0.3s ease-out;
}

@keyframes theme-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-toggle-track {
    width: 50px;
    height: 28px;
  }
  
  .theme-toggle-thumb {
    width: 24px;
    height: 24px;
  }
  
  [data-theme="dark"] .theme-toggle-thumb {
    transform: translateX(22px);
  }
  
  .theme-icon {
    font-size: 12px;
  }
}
