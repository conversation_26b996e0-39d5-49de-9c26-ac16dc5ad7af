/* Staff Profile Styles - Enhanced Modern UI */
.staff-profile {
  padding: 0;
  max-width: 100%;
  margin: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
}

.staff-profile::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Navigation Header */
.profile-navigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid rgba(203, 213, 225, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

/* Hero Section */
.profile-hero {
  position: relative;
  margin-bottom: var(--space-8);
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-8);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 32px;
  margin-top: var(--space-6);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.staff-avatar-hero {
  position: relative;
  flex-shrink: 0;
  display: inline-block;
}

.staff-avatar-hero,
.avatar-placeholder-hero {
  width: 160px;
  height: 160px;
  border-radius: 32px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
  position: relative;
}

.staff-avatar-hero::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 36px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  z-index: -1;
}

.staff-avatar-hero img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-hero {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 800;
  font-size: var(--text-4xl);
  position: relative;
}

.avatar-placeholder-hero::before {
  content: '';
  position: absolute;
  inset: 6px;
  border-radius: 26px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
}

.avatar-status {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 20;
}

.hero-info {
  flex: 1;
}

.staff-name-hero {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 var(--space-3) 0;
  letter-spacing: -0.02em;
}

.staff-role-hero {
  margin-bottom: var(--space-2);
}



.hero-stats {
  display: flex;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.hero-stat {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(203, 213, 225, 0.3);
  backdrop-filter: blur(12px);
}

.stat-icon {
  font-size: var(--text-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 16px;
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-gray-900);
  line-height: 1;
}

.stat-label {
  font-size: var(--text-sm);
  color: #64748b;
  font-weight: 500;
}

/* Profile Content */
.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-8);
  position: relative;
  z-index: 1;
}

.profile-view {
  width: 100%;
}

/* Information Cards */
.profile-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: 0;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.card-header {
  padding: var(--space-6) var(--space-6) var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  border-bottom: 1px solid rgba(203, 213, 225, 0.2);
}

.card-icon {
  font-size: var(--text-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 16px;
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.card-content {
  padding: var(--space-6);
}

/* Info Items */
.info-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) 0;
  border-bottom: 1px solid rgba(203, 213, 225, 0.2);
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  font-size: var(--text-xl);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 12px;
  color: #3b82f6;
  flex-shrink: 0;
}

.info-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.info-label {
  font-size: var(--text-sm);
  color: #64748b;
  font-weight: 500;
}

.info-value {
  font-size: var(--text-base);
  color: var(--color-gray-900);
  font-weight: 600;
}

/* Specialty Grid */
.specialty-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.specialty-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.specialty-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.specialty-icon {
  font-size: var(--text-lg);
  color: #3b82f6;
}

.specialty-name {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-900);
}

.staff-avatar-large {
  width: 160px;
  height: 160px;
  border-radius: 32px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
}

.staff-avatar-large::before {
  content: '';
  position: absolute;
  inset: -3px;
  border-radius: 35px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  z-index: -1;
}

.staff-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 800;
  font-size: var(--text-4xl);
  position: relative;
}

.avatar-placeholder-large::before {
  content: '';
  position: absolute;
  inset: 4px;
  border-radius: 28px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
}

/* Skills Grid */
.skills-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(203, 213, 225, 0.3);
  transition: all 0.3s ease;
}

.skill-item:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-name {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-900);
}

.skill-progress {
  width: 100%;
  height: 6px;
  background: rgba(203, 213, 225, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-beginner {
  width: 25%;
  background: linear-gradient(90deg, #ef4444, #f97316);
}

.progress-intermediate {
  width: 50%;
  background: linear-gradient(90deg, #f59e0b, #eab308);
}

.progress-advanced {
  width: 75%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.progress-expert {
  width: 100%;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
}

/* Performance Grid */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.performance-metric {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(203, 213, 225, 0.3);
  transition: all 0.3s ease;
}

.performance-metric:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.metric-icon {
  font-size: var(--text-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 16px;
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.metric-value {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-gray-900);
  line-height: 1;
}

.metric-label {
  font-size: var(--text-sm);
  color: #64748b;
  font-weight: 500;
}

/* Notes Card */
.notes-card .notes-content {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--color-gray-700);
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-4);
  border-radius: 16px;
  border: 1px solid rgba(203, 213, 225, 0.3);
}

.staff-basic-info {
  flex: 1;
}

.staff-name {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 var(--space-3) 0;
  letter-spacing: -0.02em;
}

.staff-id {
  font-size: var(--text-lg);
  color: #64748b;
  margin: 0 0 var(--space-4) 0;
  font-weight: 500;
  opacity: 0.8;
}

.staff-badges {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
}

.staff-quick-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.quick-stat {
  text-align: center;
  padding: var(--space-3);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  min-width: 120px;
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--color-gray-600);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-1);
}

.stat-value {
  display: block;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
}

/* Profile Details */
.profile-details {
  display: grid;
  gap: var(--space-6);
}

.detail-section {
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
  padding-bottom: var(--space-2);
  border-bottom: 2px solid var(--color-gray-200);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-label {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-medium);
}

.detail-value {
  font-size: var(--text-base);
  color: var(--color-gray-900);
  font-weight: var(--font-medium);
}

/* Specialties */
.specialty-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.specialty-tag {
  padding: var(--space-2) var(--space-3);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* Skills */
.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.skill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.skill-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-900);
}

.skill-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.skill-beginner {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.skill-intermediate {
  background: var(--color-blue-100);
  color: var(--color-blue-700);
}

.skill-advanced {
  background: var(--color-orange-100);
  color: var(--color-orange-700);
}

.skill-expert {
  background: var(--color-green-100);
  color: var(--color-green-700);
}

/* Performance Overview */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.performance-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: var(--space-3);
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.performance-label {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-medium);
}

.performance-value {
  font-size: var(--text-lg);
  color: var(--color-gray-900);
  font-weight: var(--font-bold);
}

/* Notes */
.notes-content {
  font-size: var(--text-base);
  color: var(--color-gray-700);
  line-height: 1.6;
  margin: 0;
  padding: var(--space-4);
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

/* Form Styles (for editing mode) */
.profile-form {
  width: 100%;
}

.form-grid {
  display: grid;
  gap: var(--space-6);
}

.form-section {
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: white;
  transition: all var(--transition-base);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-error {
  border-color: var(--color-red-500);
}

.form-input-error:focus {
  border-color: var(--color-red-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.error-message {
  display: block;
  font-size: var(--text-xs);
  color: var(--color-red-600);
  margin-top: var(--space-1);
  font-weight: var(--font-medium);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-gray-200);
  color: var(--color-gray-900);
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(135deg, var(--color-red-500), var(--color-red-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-red-600), var(--color-red-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: var(--text-base);
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status and Role Badges */
.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background: var(--color-green-100);
  color: var(--color-green-700);
}

.status-inactive {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.status-leave {
  background: var(--color-orange-100);
  color: var(--color-orange-700);
}

.status-terminated {
  background: var(--color-red-100);
  color: var(--color-red-700);
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.role-icon {
  font-size: var(--text-base);
}

/* Responsive Design */
@media (max-width: 768px) {
  .staff-profile {
    padding: var(--space-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .staff-overview {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-4);
  }

  .staff-quick-stats {
    flex-direction: row;
    justify-content: center;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .performance-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Additional Button Styles for Profile */
.btn-ghost {
  background: transparent;
  color: #64748b;
  border: 1px solid transparent;
  padding: var(--space-3) var(--space-4);
  border-radius: 12px;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-ghost:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.8);
  color: #334155;
  border-color: rgba(203, 213, 225, 0.3);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  padding: var(--space-3) var(--space-4);
  border-radius: 12px;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-outline:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
}

.btn-danger-outline {
  background: transparent;
  color: #ef4444;
  border: 2px solid #ef4444;
  padding: var(--space-3) var(--space-4);
  border-radius: 12px;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-danger-outline:hover:not(:disabled) {
  background: #ef4444;
  color: white;
  transform: translateY(-2px);
  box-shadow:
    0 10px 15px -3px rgba(239, 68, 68, 0.3),
    0 4px 6px -2px rgba(239, 68, 68, 0.2);
}

/* Enhanced Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: 20px;
  font-size: var(--text-sm);
  font-weight: 600;
  text-transform: capitalize;
  letter-spacing: 0.02em;
  position: relative;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.status-icon {
  font-size: var(--text-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-label {
  font-weight: 600;
  white-space: nowrap;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.status-active {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.15));
  color: #065f46;
  border-color: rgba(16, 185, 129, 0.3);
}

.status-active .status-indicator {
  background: #10b981;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.8),
    0 0 8px rgba(16, 185, 129, 0.4);
}

.status-inactive {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.15), rgba(75, 85, 99, 0.15));
  color: #374151;
  border-color: rgba(107, 114, 128, 0.3);
}

.status-inactive .status-indicator {
  background: #6b7280;
  animation: none;
}

.status-leave {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.15));
  color: #92400e;
  border-color: rgba(245, 158, 11, 0.3);
}

.status-leave .status-indicator {
  background: #f59e0b;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.8),
    0 0 8px rgba(245, 158, 11, 0.4);
}

.status-terminated {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
  color: #991b1b;
  border-color: rgba(239, 68, 68, 0.3);
}

.status-terminated .status-indicator {
  background: #ef4444;
  animation: none;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.8),
    0 0 8px rgba(239, 68, 68, 0.4);
}

/* Compact Status Badge for Avatar Overlay */
.status-compact {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #ffffff;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.3);
  position: relative;
}

.status-compact .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffffff !important;
  animation: pulse 2s infinite;
}

.status-compact.status-inactive .status-indicator,
.status-compact.status-terminated .status-indicator {
  animation: none;
}

/* Compact Status Colors */
.status-compact.status-active {
  background: #10b981;
  border-color: #ffffff;
}

.status-compact.status-inactive {
  background: #6b7280;
  border-color: #ffffff;
}

.status-compact.status-leave {
  background: #f59e0b;
  border-color: #ffffff;
}

.status-compact.status-terminated {
  background: #ef4444;
  border-color: #ffffff;
}



@media (max-width: 480px) {
  .staff-profile {
    padding: var(--space-3);
  }

  .profile-header,
  .profile-content,
  .detail-section,
  .form-section {
    padding: var(--space-4);
  }

  .staff-name {
    font-size: var(--text-2xl);
  }

  .staff-avatar-large {
    width: 80px;
    height: 80px;
  }

  .avatar-placeholder-large {
    font-size: var(--text-xl);
  }

  .staff-quick-stats {
    flex-direction: column;
  }

  .header-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
}
