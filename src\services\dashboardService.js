// Dashboard Data Service
// This service provides mock data for the dashboard
// In a real application, this would fetch data from an API

class DashboardService {
  constructor() {
    this.mockData = this.generateMockData();
  }

  generateMockData() {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    return {
      stats: {
        todayAppointments: 12,
        totalCustomers: 248,
        monthlyRevenue: 12450,
        staffMembers: 8,
        pendingAppointments: 5,
        completedToday: 7,
        avgServiceTime: 45,
        customerSatisfaction: 4.8
      },
      recentActivities: [
        {
          id: 1,
          type: 'appointment',
          icon: '📅',
          message: 'New appointment booked by <PERSON>',
          time: '5 minutes ago',
          timestamp: new Date(Date.now() - 5 * 60 * 1000)
        },
        {
          id: 2,
          type: 'payment',
          icon: '💰',
          message: 'Payment received from <PERSON> - $85',
          time: '12 minutes ago',
          timestamp: new Date(Date.now() - 12 * 60 * 1000)
        },
        {
          id: 3,
          type: 'customer',
          icon: '👤',
          message: 'New customer registered - <PERSON>',
          time: '25 minutes ago',
          timestamp: new Date(Date.now() - 25 * 60 * 1000)
        },
        {
          id: 4,
          type: 'service',
          icon: '✂️',
          message: 'Hair cut service completed for <PERSON> Doe',
          time: '1 hour ago',
          timestamp: new Date(Date.now() - 60 * 60 * 1000)
        },
        {
          id: 5,
          type: 'inventory',
          icon: '📦',
          message: 'Low stock alert: Hair Shampoo (5 units left)',
          time: '2 hours ago',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
      ],
      chartData: {
        revenueChart: {
          labels: this.getLast7Days(),
          datasets: [{
            label: 'Daily Revenue',
            data: [850, 920, 1100, 980, 1250, 1180, 1350],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        appointmentsChart: {
          labels: this.getLast7Days(),
          datasets: [{
            label: 'Appointments',
            data: [8, 12, 15, 10, 18, 14, 16],
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        servicesChart: {
          labels: ['Hair Cut', 'Hair Color', 'Facial', 'Manicure', 'Massage'],
          datasets: [{
            label: 'Service Popularity',
            data: [35, 25, 20, 15, 5],
            backgroundColor: [
              '#3498db',
              '#2ecc71',
              '#f39c12',
              '#e74c3c',
              '#9b59b6'
            ],
            borderWidth: 0
          }]
        },
        monthlyRevenueChart: {
          labels: this.getLast6Months(),
          datasets: [{
            label: 'Monthly Revenue',
            data: [8500, 9200, 11000, 10800, 12500, 12450],
            backgroundColor: 'rgba(52, 152, 219, 0.8)',
            borderColor: '#3498db',
            borderWidth: 1
          }]
        }
      },
      upcomingAppointments: [
        {
          id: 1,
          customerName: 'Sarah Johnson',
          service: 'Hair Cut & Style',
          time: '10:00 AM',
          duration: '1h 30m',
          staff: 'Alice Smith'
        },
        {
          id: 2,
          customerName: 'Michael Brown',
          service: 'Hair Color',
          time: '11:30 AM',
          duration: '2h',
          staff: 'Bob Wilson'
        },
        {
          id: 3,
          customerName: 'Emma Wilson',
          service: 'Facial Treatment',
          time: '2:00 PM',
          duration: '1h',
          staff: 'Carol Davis'
        },
        {
          id: 4,
          customerName: 'John Doe',
          service: 'Massage',
          time: '3:30 PM',
          duration: '45m',
          staff: 'David Lee'
        }
      ]
    };
  }

  getLast7Days() {
    const days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
    }
    return days;
  }

  getLast6Months() {
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date.toLocaleDateString('en-US', { month: 'short' }));
    }
    return months;
  }

  // API-like methods
  async getDashboardStats() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      success: true,
      data: this.mockData.stats
    };
  }

  async getRecentActivities(limit = 5) {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      success: true,
      data: this.mockData.recentActivities.slice(0, limit)
    };
  }

  async getChartData() {
    await new Promise(resolve => setTimeout(resolve, 400));
    return {
      success: true,
      data: this.mockData.chartData
    };
  }

  async getUpcomingAppointments(limit = 4) {
    await new Promise(resolve => setTimeout(resolve, 200));
    return {
      success: true,
      data: this.mockData.upcomingAppointments.slice(0, limit)
    };
  }

  // Real-time data simulation
  updateStats() {
    // Simulate real-time updates
    this.mockData.stats.todayAppointments += Math.floor(Math.random() * 2);
    this.mockData.stats.monthlyRevenue += Math.floor(Math.random() * 100);
    
    // Add new activity occasionally
    if (Math.random() > 0.7) {
      const newActivity = {
        id: Date.now(),
        type: 'appointment',
        icon: '📅',
        message: 'New appointment just booked',
        time: 'Just now',
        timestamp: new Date()
      };
      this.mockData.recentActivities.unshift(newActivity);
      this.mockData.recentActivities = this.mockData.recentActivities.slice(0, 10);
    }
  }

  // Format currency
  formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  // Format time ago
  formatTimeAgo(timestamp) {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
}

// Create and export a singleton instance
const dashboardService = new DashboardService();
export default dashboardService;
