/* Modern Footer Styles */
.footer-section {
  background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-gray-800) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.footer-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 4rem 2rem 0;
  position: relative;
  z-index: 1;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.2fr;
  gap: 3rem;
  align-items: flex-start;
  margin-bottom: 3rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.brand-column {
  max-width: 400px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.footer-logo-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.footer-logo-text {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff, var(--color-primary-300));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-logo-accent {
  color: var(--color-accent-400);
}

.footer-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 2rem;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 1px;
}

.footer-description {
  color: var(--color-gray-300);
  line-height: 1.7;
  font-size: var(--font-size-sm);
  margin-bottom: 1.5rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--color-gray-300);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link svg {
  width: 18px;
  height: 18px;
}

.social-link:hover {
  background: var(--color-primary-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-link {
  color: var(--color-gray-300);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  position: relative;
}

.footer-link::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background: var(--color-primary-400);
  transition: width 0.3s ease;
}

.footer-link:hover {
  color: white;
  transform: translateX(4px);
}

.footer-link:hover::before {
  width: 100%;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--color-primary-400);
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.contact-icon svg {
  width: 18px;
  height: 18px;
}

.contact-text {
  color: var(--color-gray-300);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-text span {
  display: block;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
  margin-top: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-link {
  color: var(--color-gray-400);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}

.footer-bottom-link:hover {
  color: var(--color-primary-400);
}

.footer-text {
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}

.footer-bottom-text {
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
  text-transform: lowercase;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2.5rem;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 3rem 1.5rem 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    margin-bottom: 2rem;
  }

  .brand-column {
    max-width: none;
  }

  .footer-logo-text {
    font-size: 1.75rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer-bottom-links {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 2rem 1rem 0;
  }

  .footer-content {
    gap: 2rem;
  }

  .footer-logo {
    gap: 0.5rem;
  }

  .footer-logo-icon {
    font-size: 1.5rem;
  }

  .footer-logo-text {
    font-size: 1.5rem;
  }

  .footer-social {
    gap: 0.75rem;
  }

  .social-link {
    width: 36px;
    height: 36px;
  }

  .social-link svg {
    width: 16px;
    height: 16px;
  }

  .footer-bottom-links {
    flex-direction: column;
    gap: 1rem;
  }
}