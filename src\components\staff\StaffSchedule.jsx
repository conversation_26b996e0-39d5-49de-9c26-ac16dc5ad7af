import { useState, useEffect } from 'react';
import scheduleService, { DAYS_OF_WEEK } from '../../services/scheduleService';
import toast from 'react-hot-toast';

// Modern inline styles
const modernStyles = {
  container: {
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    padding: 0
  },
  header: {
    background: 'rgba(255, 255, 255, 0.95)',
    borderBottom: '1px solid #e2e8f0',
    position: 'sticky',
    top: 0,
    zIndex: 100,
    backdropFilter: 'blur(20px)'
  },
  headerTop: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '1rem 2rem',
    borderBottom: '1px solid #f1f5f9'
  },
  backBtn: {
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    padding: '0.5rem 1rem',
    background: '#f8fafc',
    border: '1px solid #e2e8f0',
    borderRadius: '0.5rem',
    color: '#64748b',
    fontWeight: 500,
    cursor: 'pointer',
    transition: 'all 0.2s ease'
  },
  headerMain: {
    padding: '2rem'
  },
  staffInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '1.5rem'
  },
  avatar: {
    position: 'relative',
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    overflow: 'hidden',
    border: '4px solid white',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
    color: 'white',
    fontSize: '1.5rem',
    fontWeight: 700
  },
  staffName: {
    fontSize: '2rem',
    fontWeight: 700,
    color: '#1e293b',
    margin: '0 0 0.5rem 0'
  },
  statsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '1.5rem',
    padding: '0 2rem 2rem'
  },
  statCard: {
    background: 'white',
    borderRadius: '1rem',
    padding: '1.5rem',
    border: '1px solid #e2e8f0',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden'
  },
  tabNav: {
    display: 'flex',
    background: 'white',
    borderRadius: '1rem',
    padding: '0.5rem',
    border: '1px solid #e2e8f0',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    margin: '0 2rem 2rem'
  },
  tab: {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    padding: '1rem 1.5rem',
    border: 'none',
    background: 'transparent',
    borderRadius: '0.75rem',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    textAlign: 'left'
  },
  tabActive: {
    background: '#3b82f6',
    color: 'white',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
  },
  content: {
    background: 'white',
    borderRadius: '1rem',
    padding: '2rem',
    margin: '0 2rem',
    border: '1px solid #e2e8f0',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  },
  scheduleGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
    gap: '1.5rem'
  },
  dayCard: {
    background: '#f8fafc',
    borderRadius: '1rem',
    padding: '1.5rem',
    border: '2px solid #e2e8f0',
    transition: 'all 0.3s ease'
  },
  dayCardActive: {
    borderColor: '#3b82f6',
    background: '#f0f9ff'
  }
};

const StaffSchedule = ({ staff, onBack, canEdit }) => {
  const [schedule, setSchedule] = useState(null);
  const [activeTab, setActiveTab] = useState('regular'); // 'regular', 'timeoff', 'calendar'
  const [isEditing, setIsEditing] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState({});
  const [showTimeOffForm, setShowTimeOffForm] = useState(false);
  const [timeOffForm, setTimeOffForm] = useState({
    type: 'vacation',
    startDate: '',
    endDate: '',
    reason: ''
  });
  const [selectedWeek, setSelectedWeek] = useState(new Date());

  useEffect(() => {
    loadSchedule();
  }, [staff.id]);

  const loadSchedule = () => {
    const staffSchedule = scheduleService.getStaffSchedule(staff.id);
    setSchedule(staffSchedule);
    setEditingSchedule(staffSchedule.regularSchedule);
  };

  const handleSaveRegularSchedule = () => {
    try {
      scheduleService.updateRegularSchedule(staff.id, editingSchedule);
      setSchedule(prev => ({ ...prev, regularSchedule: editingSchedule }));
      setIsEditing(false);
      toast.success('Schedule updated successfully');
    } catch (error) {
      console.error('Error updating schedule:', error);
      toast.error('Failed to update schedule');
    }
  };

  const handleScheduleChange = (day, field, value) => {
    setEditingSchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }));
  };

  const handleTimeOffSubmit = (e) => {
    e.preventDefault();
    try {
      scheduleService.addTimeOffRequest(staff.id, timeOffForm);
      loadSchedule();
      setShowTimeOffForm(false);
      setTimeOffForm({ type: 'vacation', startDate: '', endDate: '', reason: '' });
      toast.success('Time off request submitted');
    } catch (error) {
      console.error('Error submitting time off request:', error);
      toast.error('Failed to submit time off request');
    }
  };

  const handleTimeOffStatusUpdate = (requestId, status) => {
    try {
      scheduleService.updateTimeOffStatus(staff.id, requestId, status, 'current-user');
      loadSchedule();
      toast.success(`Time off request ${status}`);
    } catch (error) {
      console.error('Error updating time off status:', error);
      toast.error('Failed to update time off status');
    }
  };

  const formatTime = (time) => {
    if (!time) return '';
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getDayName = (day) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { label: 'Pending', className: 'status-pending', icon: '⏳' },
      approved: { label: 'Approved', className: 'status-approved', icon: '✅' },
      rejected: { label: 'Rejected', className: 'status-rejected', icon: '❌' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`time-off-status ${config.className}`}>
        <span className="status-icon">{config.icon}</span>
        <span className="status-label">{config.label}</span>
      </span>
    );
  };

  const calculateWeeklyHours = () => {
    if (!schedule) return 0;

    let totalHours = 0;
    Object.values(schedule.regularSchedule).forEach(day => {
      if (day.isWorking && day.startTime && day.endTime) {
        const start = new Date(`2000-01-01T${day.startTime}`);
        const end = new Date(`2000-01-01T${day.endTime}`);
        totalHours += (end - start) / (1000 * 60 * 60);
      }
    });

    return Math.round(totalHours * 100) / 100;
  };

  if (!schedule) {
    return (
      <div className="staff-schedule">
        <div className="loading">Loading schedule...</div>
      </div>
    );
  }

  return (
    <div style={modernStyles.container}>
      {/* Modern Header */}
      <div style={modernStyles.header}>
        <div style={modernStyles.headerTop}>
          <button style={modernStyles.backBtn} onClick={onBack}>
            <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 12H5m7-7l-7 7 7 7" />
            </svg>
            Back
          </button>

          <div style={{display: 'flex', gap: '0.75rem'}}>
            <button style={{...modernStyles.backBtn, background: '#3b82f6', color: 'white', border: 'none'}}>
              <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v18m9-9H3" />
              </svg>
              Export
            </button>
            <button style={{...modernStyles.backBtn, background: '#10b981', color: 'white', border: 'none'}}>
              <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Quick Schedule
            </button>
          </div>
        </div>

        <div style={modernStyles.headerMain}>
          <div style={modernStyles.staffInfo}>
            <div style={modernStyles.avatar}>
              {staff.avatar ? (
                <img src={staff.avatar} alt={`${staff.firstName} ${staff.lastName}`} style={{width: '100%', height: '100%', objectFit: 'cover'}} />
              ) : (
                <div style={modernStyles.avatarPlaceholder}>
                  {staff.firstName[0]}{staff.lastName[0]}
                </div>
              )}
              <div style={{
                position: 'absolute',
                bottom: '4px',
                right: '4px',
                width: '16px',
                height: '16px',
                background: '#10b981',
                border: '3px solid white',
                borderRadius: '50%'
              }}></div>
            </div>

            <div style={{flex: 1}}>
              <h1 style={modernStyles.staffName}>{staff.firstName} {staff.lastName}</h1>
              <div style={{marginBottom: '0.75rem'}}>
                <span style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  background: '#f1f5f9',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '1rem',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: '#475569'
                }}>
                  <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  {staff.role || 'Stylist'}
                </span>
              </div>
              <p style={{color: '#64748b', lineHeight: 1.5, margin: 0}}>
                Manage weekly schedules, time-off requests, and availability settings
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Dashboard Stats */}
      <div style={modernStyles.statsGrid}>
        <div style={{...modernStyles.statCard, borderTop: '4px solid #3b82f6'}}>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem'}}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#dbeafe',
              color: '#3b82f6'
            }}>
              <svg style={{width: '24px', height: '24px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              fontSize: '0.75rem',
              fontWeight: 600,
              padding: '0.25rem 0.5rem',
              borderRadius: '0.375rem',
              background: '#d1fae5',
              color: '#059669'
            }}>
              <svg style={{width: '12px', height: '12px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                <polyline points="17,6 23,6 23,12"/>
              </svg>
              +12%
            </div>
          </div>
          <div>
            <h3 style={{fontSize: '2.5rem', fontWeight: 700, color: '#1e293b', lineHeight: 1, margin: 0}}>
              {calculateWeeklyHours()}
            </h3>
            <p style={{fontSize: '0.875rem', fontWeight: 600, color: '#64748b', margin: '0.5rem 0'}}>Weekly Hours</p>
            <div style={{marginTop: '0.75rem'}}>
              <div style={{
                height: '6px',
                background: '#f1f5f9',
                borderRadius: '3px',
                overflow: 'hidden',
                marginBottom: '0.5rem'
              }}>
                <div style={{
                  height: '100%',
                  background: '#3b82f6',
                  borderRadius: '3px',
                  width: `${Math.min((calculateWeeklyHours() / 40) * 100, 100)}%`,
                  transition: 'width 0.5s ease'
                }}></div>
              </div>
              <span style={{fontSize: '0.75rem', color: '#64748b', fontWeight: 500}}>
                {Math.round((calculateWeeklyHours() / 40) * 100)}% of target
              </span>
            </div>
          </div>
        </div>

        <div style={{...modernStyles.statCard, borderTop: '4px solid #f59e0b'}}>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem'}}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#fef3c7',
              color: '#f59e0b',
              position: 'relative'
            }}>
              <svg style={{width: '24px', height: '24px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              {schedule.timeOffRequests.filter(r => r.status === 'pending').length > 0 && (
                <div style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '-8px',
                  background: '#ef4444',
                  color: 'white',
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.75rem',
                  fontWeight: 700,
                  border: '3px solid white'
                }}>
                  {schedule.timeOffRequests.filter(r => r.status === 'pending').length}
                </div>
              )}
            </div>
          </div>
          <div>
            <h3 style={{fontSize: '2.5rem', fontWeight: 700, color: '#1e293b', lineHeight: 1, margin: 0}}>
              {schedule.timeOffRequests.filter(r => r.status === 'pending').length}
            </h3>
            <p style={{fontSize: '0.875rem', fontWeight: 600, color: '#64748b', margin: '0.5rem 0'}}>Pending Requests</p>
            <p style={{fontSize: '0.75rem', color: '#94a3b8', margin: 0}}>Awaiting your approval</p>
          </div>
        </div>

        <div style={{...modernStyles.statCard, borderTop: '4px solid #10b981'}}>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem'}}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#d1fae5',
              color: '#10b981'
            }}>
              <svg style={{width: '24px', height: '24px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                <polyline points="22,4 12,14.01 9,11.01"/>
              </svg>
            </div>
          </div>
          <div>
            <h3 style={{fontSize: '2.5rem', fontWeight: 700, color: '#1e293b', lineHeight: 1, margin: 0}}>
              {schedule.timeOffRequests.filter(r => r.status === 'approved').length}
            </h3>
            <p style={{fontSize: '0.875rem', fontWeight: 600, color: '#64748b', margin: '0.5rem 0'}}>Approved Time Off</p>
            <p style={{fontSize: '0.75rem', color: '#94a3b8', margin: 0}}>This month</p>
          </div>
        </div>

        <div style={{...modernStyles.statCard, borderTop: '4px solid #06b6d4'}}>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem'}}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#cffafe',
              color: '#06b6d4'
            }}>
              <svg style={{width: '24px', height: '24px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
            </div>
          </div>
          <div>
            <h3 style={{fontSize: '2.5rem', fontWeight: 700, color: '#1e293b', lineHeight: 1, margin: 0}}>
              {Object.values(schedule.regularSchedule).filter(day => day.isWorking).length}
            </h3>
            <p style={{fontSize: '0.875rem', fontWeight: 600, color: '#64748b', margin: '0.5rem 0'}}>Working Days</p>
            <p style={{fontSize: '0.75rem', color: '#94a3b8', margin: 0}}>Per week</p>
          </div>
        </div>
      </div>

      {/* Modern Tab Navigation */}
      <div style={modernStyles.tabNav}>
        <button
          style={{
            ...modernStyles.tab,
            ...(activeTab === 'regular' ? modernStyles.tabActive : {})
          }}
          onClick={() => setActiveTab('regular')}
        >
          <div style={{
            position: 'relative',
            width: '40px',
            height: '40px',
            borderRadius: '0.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: activeTab === 'regular' ? 'rgba(255, 255, 255, 0.2)' : '#f1f5f9',
            color: activeTab === 'regular' ? 'white' : '#64748b',
            transition: 'all 0.2s ease'
          }}>
            <svg style={{width: '20px', height: '20px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <div style={{display: 'flex', flexDirection: 'column', gap: '0.125rem'}}>
            <span style={{
              fontSize: '0.875rem',
              fontWeight: 600,
              color: activeTab === 'regular' ? 'white' : '#1e293b'
            }}>Schedule</span>
            <span style={{
              fontSize: '0.75rem',
              color: activeTab === 'regular' ? 'rgba(255, 255, 255, 0.8)' : '#64748b'
            }}>Weekly hours</span>
          </div>
        </button>

        <button
          style={{
            ...modernStyles.tab,
            ...(activeTab === 'timeoff' ? modernStyles.tabActive : {})
          }}
          onClick={() => setActiveTab('timeoff')}
        >
          <div style={{
            position: 'relative',
            width: '40px',
            height: '40px',
            borderRadius: '0.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: activeTab === 'timeoff' ? 'rgba(255, 255, 255, 0.2)' : '#f1f5f9',
            color: activeTab === 'timeoff' ? 'white' : '#64748b',
            transition: 'all 0.2s ease'
          }}>
            <svg style={{width: '20px', height: '20px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            {schedule.timeOffRequests.filter(r => r.status === 'pending').length > 0 && (
              <div style={{
                position: 'absolute',
                top: '-6px',
                right: '-6px',
                background: '#ef4444',
                color: 'white',
                width: '18px',
                height: '18px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.625rem',
                fontWeight: 700,
                border: '2px solid white'
              }}>
                {schedule.timeOffRequests.filter(r => r.status === 'pending').length}
              </div>
            )}
          </div>
          <div style={{display: 'flex', flexDirection: 'column', gap: '0.125rem'}}>
            <span style={{
              fontSize: '0.875rem',
              fontWeight: 600,
              color: activeTab === 'timeoff' ? 'white' : '#1e293b'
            }}>Time Off</span>
            <span style={{
              fontSize: '0.75rem',
              color: activeTab === 'timeoff' ? 'rgba(255, 255, 255, 0.8)' : '#64748b'
            }}>Requests & approvals</span>
          </div>
        </button>

        <button
          style={{
            ...modernStyles.tab,
            ...(activeTab === 'calendar' ? modernStyles.tabActive : {})
          }}
          onClick={() => setActiveTab('calendar')}
        >
          <div style={{
            position: 'relative',
            width: '40px',
            height: '40px',
            borderRadius: '0.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: activeTab === 'calendar' ? 'rgba(255, 255, 255, 0.2)' : '#f1f5f9',
            color: activeTab === 'calendar' ? 'white' : '#64748b',
            transition: 'all 0.2s ease'
          }}>
            <svg style={{width: '20px', height: '20px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
          </div>
          <div style={{display: 'flex', flexDirection: 'column', gap: '0.125rem'}}>
            <span style={{
              fontSize: '0.875rem',
              fontWeight: 600,
              color: activeTab === 'calendar' ? 'white' : '#1e293b'
            }}>Calendar</span>
            <span style={{
              fontSize: '0.75rem',
              color: activeTab === 'calendar' ? 'rgba(255, 255, 255, 0.8)' : '#64748b'
            }}>Visual overview</span>
          </div>
        </button>
      </div>

      {/* Modern Content Area */}
      <div style={modernStyles.content}>
        {/* Modern Regular Schedule Tab */}
        {activeTab === 'regular' && (
          <div>
            <div style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'space-between',
              marginBottom: '2rem',
              paddingBottom: '1rem',
              borderBottom: '2px solid #f1f5f9'
            }}>
              <div>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: 700,
                  color: '#1e293b',
                  margin: '0 0 0.5rem 0'
                }}>Weekly Schedule</h2>
                <p style={{
                  color: '#64748b',
                  margin: 0,
                  lineHeight: 1.5
                }}>
                  Configure working hours and break times for each day of the week
                </p>
              </div>
              {canEdit && (
                <div style={{display: 'flex', gap: '0.75rem'}}>
                  {!isEditing ? (
                    <button
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.75rem 1.5rem',
                        background: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '0.5rem',
                        fontWeight: 500,
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onClick={() => setIsEditing(true)}
                    >
                      <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                      </svg>
                      Edit Schedule
                    </button>
                  ) : (
                    <div style={{display: 'flex', gap: '0.5rem'}}>
                      <button
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.75rem 1.5rem',
                          background: '#f8fafc',
                          color: '#64748b',
                          border: '1px solid #e2e8f0',
                          borderRadius: '0.5rem',
                          fontWeight: 500,
                          cursor: 'pointer',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={() => {
                          setIsEditing(false);
                          setEditingSchedule(schedule.regularSchedule);
                        }}
                      >
                        <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <line x1="18" y1="6" x2="6" y2="18"/>
                          <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                        Cancel
                      </button>
                      <button
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.75rem 1.5rem',
                          background: '#10b981',
                          color: 'white',
                          border: 'none',
                          borderRadius: '0.5rem',
                          fontWeight: 500,
                          cursor: 'pointer',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={handleSaveRegularSchedule}
                      >
                        <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                          <polyline points="17,21 17,13 7,13 7,21"/>
                          <polyline points="7,3 7,8 15,8"/>
                        </svg>
                        Save Changes
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {isEditing && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                background: '#eff6ff',
                border: '1px solid #bfdbfe',
                borderRadius: '0.75rem',
                padding: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  background: '#3b82f6',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{width: '20px', height: '20px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="12" y1="16" x2="12" y2="12"/>
                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                  </svg>
                </div>
                <div>
                  <h4 style={{margin: '0 0 0.25rem 0', color: '#1e40af', fontWeight: 600}}>Edit Mode Active</h4>
                  <p style={{margin: 0, color: '#1e40af', fontSize: '0.875rem'}}>
                    You're currently editing the weekly schedule. Make your changes and save when ready.
                  </p>
                </div>
              </div>
            )}

            <div style={modernStyles.scheduleGrid}>
              {Object.entries(DAYS_OF_WEEK).map(([, day]) => {
                const daySchedule = isEditing ? editingSchedule[day] : schedule.regularSchedule[day];
                const dayHours = daySchedule.isWorking && daySchedule.startTime && daySchedule.endTime
                  ? Math.round(((new Date(`2000-01-01T${daySchedule.endTime}`) - new Date(`2000-01-01T${daySchedule.startTime}`)) / (1000 * 60 * 60)) * 100) / 100
                  : 0;

                return (
                  <div key={day} style={{
                    ...modernStyles.dayCard,
                    ...(daySchedule.isWorking ? modernStyles.dayCardActive : {}),
                    borderLeft: `4px solid ${daySchedule.isWorking ? '#10b981' : '#e2e8f0'}`,
                    transform: isEditing ? 'scale(1.02)' : 'scale(1)',
                    boxShadow: isEditing ? '0 10px 25px rgba(0, 0, 0, 0.1)' : '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: '1.5rem'
                    }}>
                      <div style={{display: 'flex', alignItems: 'center', gap: '1rem'}}>
                        <h3 style={{
                          fontSize: '1.25rem',
                          fontWeight: 700,
                          color: '#1e293b',
                          margin: 0
                        }}>{getDayName(day)}</h3>
                        {daySchedule.isWorking && dayHours > 0 && (
                          <div style={{
                            background: '#10b981',
                            color: 'white',
                            padding: '0.25rem 0.75rem',
                            borderRadius: '1rem',
                            fontSize: '0.75rem',
                            fontWeight: 600
                          }}>{dayHours}h</div>
                        )}
                      </div>

                      <div>
                        {isEditing ? (
                          <label style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            cursor: 'pointer'
                          }}>
                            <span style={{
                              fontSize: '0.875rem',
                              fontWeight: 500,
                              color: '#64748b'
                            }}>Working</span>
                            <div style={{position: 'relative'}}>
                              <input
                                type="checkbox"
                                checked={daySchedule.isWorking}
                                onChange={(e) => handleScheduleChange(day, 'isWorking', e.target.checked)}
                                style={{display: 'none'}}
                              />
                              <div style={{
                                width: '48px',
                                height: '24px',
                                background: daySchedule.isWorking ? '#3b82f6' : '#e2e8f0',
                                borderRadius: '12px',
                                transition: 'all 0.3s ease',
                                position: 'relative'
                              }}>
                                <div style={{
                                  width: '20px',
                                  height: '20px',
                                  background: 'white',
                                  borderRadius: '50%',
                                  position: 'absolute',
                                  top: '2px',
                                  left: daySchedule.isWorking ? '26px' : '2px',
                                  transition: 'all 0.3s ease',
                                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                                }}></div>
                              </div>
                            </div>
                          </label>
                        ) : (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            padding: '0.5rem 1rem',
                            background: daySchedule.isWorking ? '#d1fae5' : '#f1f5f9',
                            color: daySchedule.isWorking ? '#065f46' : '#64748b',
                            borderRadius: '1rem',
                            fontSize: '0.875rem',
                            fontWeight: 500
                          }}>
                            <div style={{
                              width: '8px',
                              height: '8px',
                              borderRadius: '50%',
                              background: daySchedule.isWorking ? '#10b981' : '#94a3b8'
                            }}></div>
                            <span>{daySchedule.isWorking ? 'Active' : 'Off'}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {daySchedule.isWorking && (
                      <div style={{marginTop: '1rem'}}>
                        <div style={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr',
                          gap: '1rem',
                          marginBottom: '1rem'
                        }}>
                          <div>
                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              fontSize: '0.875rem',
                              fontWeight: 600,
                              color: '#374151',
                              marginBottom: '0.5rem'
                            }}>
                              <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                              </svg>
                              Start Time
                            </label>
                            {isEditing ? (
                              <input
                                type="time"
                                value={daySchedule.startTime}
                                onChange={(e) => handleScheduleChange(day, 'startTime', e.target.value)}
                                style={{
                                  width: '100%',
                                  padding: '0.75rem',
                                  border: '2px solid #e5e7eb',
                                  borderRadius: '0.5rem',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                  background: 'white',
                                  transition: 'all 0.2s ease'
                                }}
                              />
                            ) : (
                              <div style={{
                                padding: '0.75rem',
                                background: '#f9fafb',
                                border: '2px solid #f3f4f6',
                                borderRadius: '0.5rem',
                                fontSize: '0.875rem',
                                fontWeight: 600,
                                color: '#1f2937'
                              }}>
                                {formatTime(daySchedule.startTime)}
                              </div>
                            )}
                          </div>

                          <div>
                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              fontSize: '0.875rem',
                              fontWeight: 600,
                              color: '#374151',
                              marginBottom: '0.5rem'
                            }}>
                              <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                              </svg>
                              End Time
                            </label>
                            {isEditing ? (
                              <input
                                type="time"
                                value={daySchedule.endTime}
                                onChange={(e) => handleScheduleChange(day, 'endTime', e.target.value)}
                                style={{
                                  width: '100%',
                                  padding: '0.75rem',
                                  border: '2px solid #e5e7eb',
                                  borderRadius: '0.5rem',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                  background: 'white',
                                  transition: 'all 0.2s ease'
                                }}
                              />
                            ) : (
                              <div style={{
                                padding: '0.75rem',
                                background: '#f9fafb',
                                border: '2px solid #f3f4f6',
                                borderRadius: '0.5rem',
                                fontSize: '0.875rem',
                                fontWeight: 600,
                                color: '#1f2937'
                              }}>
                                {formatTime(daySchedule.endTime)}
                              </div>
                            )}
                          </div>
                        </div>

                        <div style={{marginBottom: '1rem'}}>
                          <label style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            fontSize: '0.875rem',
                            fontWeight: 600,
                            color: '#374151',
                            marginBottom: '0.5rem'
                          }}>
                            <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                              <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"/>
                              <line x1="6" y1="1" x2="6" y2="4"/>
                              <line x1="10" y1="1" x2="10" y2="4"/>
                              <line x1="14" y1="1" x2="14" y2="4"/>
                            </svg>
                            Break Time
                          </label>
                          {isEditing ? (
                            <div>
                              <input
                                type="text"
                                value={daySchedule.breakTime}
                                onChange={(e) => handleScheduleChange(day, 'breakTime', e.target.value)}
                                placeholder="12:00-13:00"
                                style={{
                                  width: '100%',
                                  padding: '0.75rem',
                                  border: '2px solid #e5e7eb',
                                  borderRadius: '0.5rem',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                  background: 'white',
                                  transition: 'all 0.2s ease',
                                  fontFamily: 'monospace'
                                }}
                              />
                              <span style={{
                                fontSize: '0.75rem',
                                color: '#6b7280',
                                fontStyle: 'italic',
                                marginTop: '0.25rem',
                                display: 'block'
                              }}>Format: HH:MM-HH:MM</span>
                            </div>
                          ) : (
                            <div style={{
                              padding: '0.75rem',
                              background: '#f9fafb',
                              border: '2px solid #f3f4f6',
                              borderRadius: '0.5rem',
                              fontSize: '0.875rem',
                              fontWeight: 600,
                              color: '#1f2937'
                            }}>
                              {daySchedule.breakTime || 'No break scheduled'}
                            </div>
                          )}
                        </div>

                        {!isEditing && daySchedule.startTime && daySchedule.endTime && (
                          <div style={{
                            background: '#f0f9ff',
                            border: '1px solid #bae6fd',
                            borderRadius: '0.5rem',
                            padding: '0.75rem',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}>
                            <span style={{
                              fontSize: '0.875rem',
                              fontWeight: 500,
                              color: '#0369a1'
                            }}>Total Hours</span>
                            <span style={{
                              fontSize: '1rem',
                              fontWeight: 700,
                              color: '#0369a1'
                            }}>{dayHours}h</span>
                          </div>
                        )}
                      </div>
                    )}

                    {!daySchedule.isWorking && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '2rem',
                        background: '#f8fafc',
                        border: '2px dashed #cbd5e1',
                        borderRadius: '0.75rem',
                        marginTop: '1rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: '0.75rem',
                          textAlign: 'center'
                        }}>
                          <div style={{
                            width: '48px',
                            height: '48px',
                            borderRadius: '50%',
                            background: '#e2e8f0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#64748b'
                          }}>
                            <svg style={{width: '24px', height: '24px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                            </svg>
                          </div>
                          <div>
                            <h4 style={{
                              margin: '0 0 0.25rem 0',
                              fontSize: '1rem',
                              fontWeight: 600,
                              color: '#475569'
                            }}>Day Off</h4>
                            <p style={{
                              margin: 0,
                              fontSize: '0.875rem',
                              color: '#64748b'
                            }}>
                              {isEditing ? 'Toggle switch to enable working hours' : 'No scheduled hours'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Time Off Requests Tab */}
        {activeTab === 'timeoff' && (
          <div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '2rem',
              paddingBottom: '1rem',
              borderBottom: '2px solid #f1f5f9'
            }}>
              <div>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: 700,
                  color: '#1e293b',
                  margin: '0 0 0.5rem 0'
                }}>Time Off Requests</h2>
                <p style={{
                  color: '#64748b',
                  margin: 0,
                  lineHeight: 1.5
                }}>
                  Manage vacation requests, sick leave, and other time-off requests
                </p>
              </div>
              {canEdit && (
                <button
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    background: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.5rem',
                    fontWeight: 500,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => setShowTimeOffForm(true)}
                >
                  <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                  </svg>
                  Request Time Off
                </button>
              )}
            </div>

            {/* Time Off Form Modal */}
            {showTimeOffForm && (
              <div style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 1000,
                padding: '1rem'
              }}>
                <div style={{
                  background: 'white',
                  borderRadius: '1rem',
                  padding: '2rem',
                  maxWidth: '500px',
                  width: '100%',
                  maxHeight: '90vh',
                  overflowY: 'auto',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '1.5rem',
                    paddingBottom: '1rem',
                    borderBottom: '2px solid #f1f5f9'
                  }}>
                    <h3 style={{
                      fontSize: '1.25rem',
                      fontWeight: 700,
                      color: '#1e293b',
                      margin: 0,
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      <svg style={{width: '1.25rem', height: '1.25rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                      </svg>
                      Request Time Off
                    </h3>
                    <button
                      style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '1.5rem',
                        color: '#64748b',
                        cursor: 'pointer',
                        padding: '0.25rem'
                      }}
                      onClick={() => setShowTimeOffForm(false)}
                    >
                      ✕
                    </button>
                  </div>

                  <form onSubmit={handleTimeOffSubmit}>
                    <div style={{marginBottom: '1.5rem'}}>
                      <label style={{
                        display: 'block',
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: '#374151',
                        marginBottom: '0.5rem'
                      }}>Type</label>
                      <select
                        value={timeOffForm.type}
                        onChange={(e) => setTimeOffForm(prev => ({ ...prev, type: e.target.value }))}
                        style={{
                          width: '100%',
                          padding: '0.75rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '0.5rem',
                          fontSize: '0.875rem',
                          background: 'white'
                        }}
                        required
                      >
                        <option value="vacation">Vacation</option>
                        <option value="sick_leave">Sick Leave</option>
                        <option value="personal">Personal Day</option>
                        <option value="emergency">Emergency</option>
                      </select>
                    </div>

                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: '1rem',
                      marginBottom: '1.5rem'
                    }}>
                      <div>
                        <label style={{
                          display: 'block',
                          fontSize: '0.875rem',
                          fontWeight: 600,
                          color: '#374151',
                          marginBottom: '0.5rem'
                        }}>Start Date</label>
                        <input
                          type="date"
                          value={timeOffForm.startDate}
                          onChange={(e) => setTimeOffForm(prev => ({ ...prev, startDate: e.target.value }))}
                          style={{
                            width: '100%',
                            padding: '0.75rem',
                            border: '2px solid #e5e7eb',
                            borderRadius: '0.5rem',
                            fontSize: '0.875rem'
                          }}
                          required
                        />
                      </div>

                      <div>
                        <label style={{
                          display: 'block',
                          fontSize: '0.875rem',
                          fontWeight: 600,
                          color: '#374151',
                          marginBottom: '0.5rem'
                        }}>End Date</label>
                        <input
                          type="date"
                          value={timeOffForm.endDate}
                          onChange={(e) => setTimeOffForm(prev => ({ ...prev, endDate: e.target.value }))}
                          style={{
                            width: '100%',
                            padding: '0.75rem',
                            border: '2px solid #e5e7eb',
                            borderRadius: '0.5rem',
                            fontSize: '0.875rem'
                          }}
                          required
                        />
                      </div>
                    </div>

                    <div style={{marginBottom: '2rem'}}>
                      <label style={{
                        display: 'block',
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: '#374151',
                        marginBottom: '0.5rem'
                      }}>Reason</label>
                      <textarea
                        value={timeOffForm.reason}
                        onChange={(e) => setTimeOffForm(prev => ({ ...prev, reason: e.target.value }))}
                        placeholder="Please provide a reason for your time off request..."
                        rows="3"
                        style={{
                          width: '100%',
                          padding: '0.75rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '0.5rem',
                          fontSize: '0.875rem',
                          resize: 'vertical',
                          minHeight: '80px'
                        }}
                        required
                      />
                    </div>

                    <div style={{
                      display: 'flex',
                      gap: '0.75rem',
                      justifyContent: 'flex-end',
                      paddingTop: '1rem',
                      borderTop: '1px solid #f1f5f9'
                    }}>
                      <button
                        type="button"
                        style={{
                          padding: '0.75rem 1.5rem',
                          background: '#f8fafc',
                          color: '#64748b',
                          border: '1px solid #e2e8f0',
                          borderRadius: '0.5rem',
                          fontWeight: 500,
                          cursor: 'pointer'
                        }}
                        onClick={() => setShowTimeOffForm(false)}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.75rem 1.5rem',
                          background: '#10b981',
                          color: 'white',
                          border: 'none',
                          borderRadius: '0.5rem',
                          fontWeight: 500,
                          cursor: 'pointer'
                        }}
                      >
                        <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <line x1="22" y1="2" x2="11" y2="13"/>
                          <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                        </svg>
                        Submit Request
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Time Off Requests List */}
            <div>
              {schedule.timeOffRequests.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem',
                  color: '#64748b'
                }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '50%',
                    background: '#f1f5f9',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 1.5rem',
                    color: '#94a3b8'
                  }}>
                    <svg style={{width: '40px', height: '40px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: 600,
                    color: '#1e293b',
                    margin: '0 0 0.5rem 0'
                  }}>No Time Off Requests</h3>
                  <p style={{
                    fontSize: '1rem',
                    margin: '0 0 1.5rem 0'
                  }}>No time off requests have been submitted yet.</p>
                  {canEdit && (
                    <button
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.75rem 1.5rem',
                        background: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '0.5rem',
                        fontWeight: 500,
                        cursor: 'pointer',
                        margin: '0 auto'
                      }}
                      onClick={() => setShowTimeOffForm(true)}
                    >
                      <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                      </svg>
                      Request Time Off
                    </button>
                  )}
                </div>
              ) : (
                <div style={{
                  display: 'grid',
                  gap: '1rem'
                }}>
                  {schedule.timeOffRequests.map((request) => (
                    <div key={request.id} style={{
                      background: '#f8fafc',
                      border: '2px solid #e2e8f0',
                      borderRadius: '1rem',
                      padding: '1.5rem',
                      transition: 'all 0.2s ease'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.75rem'
                        }}>
                          <span style={{fontSize: '1.25rem'}}>
                            {request.type === 'vacation' ? '🏖️' :
                             request.type === 'sick_leave' ? '🤒' :
                             request.type === 'personal' ? '👤' : '🚨'}
                          </span>
                          <span style={{
                            fontSize: '1rem',
                            fontWeight: 600,
                            color: '#1e293b'
                          }}>
                            {request.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.5rem 1rem',
                          borderRadius: '1rem',
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          background: request.status === 'pending' ? '#fef3c7' :
                                     request.status === 'approved' ? '#d1fae5' : '#fee2e2',
                          color: request.status === 'pending' ? '#92400e' :
                                 request.status === 'approved' ? '#065f46' : '#991b1b'
                        }}>
                          <span>
                            {request.status === 'pending' ? '⏳' :
                             request.status === 'approved' ? '✅' : '❌'}
                          </span>
                          <span>{request.status.charAt(0).toUpperCase() + request.status.slice(1)}</span>
                        </div>
                      </div>

                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem'
                        }}>
                          <svg style={{width: '1rem', height: '1rem'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                            <line x1="16" y1="2" x2="16" y2="6"/>
                            <line x1="8" y1="2" x2="8" y2="6"/>
                            <line x1="3" y1="10" x2="21" y2="10"/>
                          </svg>
                          <span style={{
                            fontSize: '0.875rem',
                            color: '#64748b'
                          }}>
                            {new Date(request.startDate).toLocaleDateString()} - {new Date(request.endDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div style={{
                          background: '#3b82f6',
                          color: 'white',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '1rem',
                          fontSize: '0.75rem',
                          fontWeight: 600
                        }}>
                          {Math.ceil((new Date(request.endDate) - new Date(request.startDate)) / (1000 * 60 * 60 * 24)) + 1} day(s)
                        </div>
                      </div>

                      <div style={{marginBottom: '1rem'}}>
                        <p style={{
                          fontSize: '0.875rem',
                          color: '#64748b',
                          margin: 0,
                          lineHeight: 1.5
                        }}>{request.reason}</p>
                      </div>

                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        fontSize: '0.75rem',
                        color: '#94a3b8',
                        marginBottom: canEdit && request.status === 'pending' ? '1rem' : 0
                      }}>
                        <span>Requested: {new Date(request.requestedDate).toLocaleDateString()}</span>
                        {request.approvedDate && (
                          <span>Approved: {new Date(request.approvedDate).toLocaleDateString()}</span>
                        )}
                      </div>

                      {canEdit && request.status === 'pending' && (
                        <div style={{
                          display: 'flex',
                          gap: '0.5rem',
                          justifyContent: 'flex-end'
                        }}>
                          <button
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 1rem',
                              background: '#10b981',
                              color: 'white',
                              border: 'none',
                              borderRadius: '0.5rem',
                              fontSize: '0.875rem',
                              fontWeight: 500,
                              cursor: 'pointer'
                            }}
                            onClick={() => handleTimeOffStatusUpdate(request.id, 'approved')}
                          >
                            <span>✅</span>
                            Approve
                          </button>
                          <button
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 1rem',
                              background: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '0.5rem',
                              fontSize: '0.875rem',
                              fontWeight: 500,
                              cursor: 'pointer'
                            }}
                            onClick={() => handleTimeOffStatusUpdate(request.id, 'rejected')}
                          >
                            <span>❌</span>
                            Reject
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Calendar View Tab */}
        {activeTab === 'calendar' && (
          <div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '2rem',
              paddingBottom: '1rem',
              borderBottom: '2px solid #f1f5f9'
            }}>
              <div>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: 700,
                  color: '#1e293b',
                  margin: '0 0 0.5rem 0'
                }}>Calendar View</h2>
                <p style={{
                  color: '#64748b',
                  margin: 0,
                  lineHeight: 1.5
                }}>
                  Visual overview of weekly schedules and time-off periods
                </p>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <button
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    background: '#f8fafc',
                    color: '#64748b',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    cursor: 'pointer'
                  }}
                  onClick={() => {
                    const newWeek = new Date(selectedWeek);
                    newWeek.setDate(newWeek.getDate() - 7);
                    setSelectedWeek(newWeek);
                  }}
                >
                  ← Previous Week
                </button>
                <span style={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  color: '#1e293b'
                }}>
                  Week of {selectedWeek.toLocaleDateString()}
                </span>
                <button
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    background: '#f8fafc',
                    color: '#64748b',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    cursor: 'pointer'
                  }}
                  onClick={() => {
                    const newWeek = new Date(selectedWeek);
                    newWeek.setDate(newWeek.getDate() + 7);
                    setSelectedWeek(newWeek);
                  }}
                >
                  Next Week →
                </button>
              </div>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(7, 1fr)',
              gap: '1rem',
              marginBottom: '2rem'
            }}>
              {Array.from({ length: 7 }, (_, i) => {
                const date = new Date(selectedWeek);
                date.setDate(date.getDate() - date.getDay() + i);
                const dateStr = date.toISOString().split('T')[0];
                const dayName = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
                const daySchedule = schedule.regularSchedule[dayName];

                const timeOff = schedule.timeOffRequests.find(r =>
                  r.status === 'approved' &&
                  dateStr >= r.startDate &&
                  dateStr <= r.endDate
                );

                const exception = schedule.scheduleExceptions.find(e => e.date === dateStr);

                return (
                  <div key={i} style={{
                    background: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '1rem',
                    padding: '1rem',
                    minHeight: '120px',
                    transition: 'all 0.2s ease'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.75rem',
                      paddingBottom: '0.5rem',
                      borderBottom: '1px solid #e2e8f0'
                    }}>
                      <span style={{
                        fontSize: '0.875rem',
                        fontWeight: 500,
                        color: '#64748b'
                      }}>{date.toLocaleDateString('en-US', { weekday: 'short' })}</span>
                      <span style={{
                        fontSize: '1.25rem',
                        fontWeight: 700,
                        color: '#1e293b'
                      }}>{date.getDate()}</span>
                    </div>

                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.5rem'
                    }}>
                      {timeOff ? (
                        <div style={{
                          padding: '0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem',
                          textAlign: 'center',
                          background: '#fed7aa',
                          color: '#9a3412'
                        }}>
                          <span style={{display: 'block', fontSize: '1rem', marginBottom: '0.25rem'}}>🏖️</span>
                          <span style={{fontWeight: 600, display: 'block'}}>Time Off</span>
                          <span style={{fontSize: '0.625rem', opacity: 0.8}}>{timeOff.reason}</span>
                        </div>
                      ) : exception ? (
                        <div style={{
                          padding: '0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem',
                          textAlign: 'center',
                          background: '#e0e7ff',
                          color: '#3730a3'
                        }}>
                          <span style={{display: 'block', fontSize: '1rem', marginBottom: '0.25rem'}}>⚡</span>
                          <span style={{fontWeight: 600, display: 'block'}}>
                            {formatTime(exception.startTime)} - {formatTime(exception.endTime)}
                          </span>
                          <span style={{fontSize: '0.625rem', opacity: 0.8}}>{exception.reason}</span>
                        </div>
                      ) : daySchedule?.isWorking ? (
                        <div style={{
                          padding: '0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem',
                          textAlign: 'center',
                          background: '#dcfce7',
                          color: '#166534'
                        }}>
                          <span style={{display: 'block', fontSize: '1rem', marginBottom: '0.25rem'}}>💼</span>
                          <span style={{fontWeight: 600, display: 'block'}}>
                            {formatTime(daySchedule.startTime)} - {formatTime(daySchedule.endTime)}
                          </span>
                          {daySchedule.breakTime && (
                            <span style={{fontSize: '0.625rem', opacity: 0.8, display: 'block'}}>
                              Break: {daySchedule.breakTime}
                            </span>
                          )}
                        </div>
                      ) : (
                        <div style={{
                          padding: '0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem',
                          textAlign: 'center',
                          background: '#f1f5f9',
                          color: '#64748b'
                        }}>
                          <span style={{display: 'block', fontSize: '1rem', marginBottom: '0.25rem'}}>😴</span>
                          <span style={{fontWeight: 600}}>Day Off</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Week Summary */}
            <div style={{
              background: '#f8fafc',
              border: '2px solid #e2e8f0',
              borderRadius: '1rem',
              padding: '1.5rem'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: 700,
                color: '#1e293b',
                margin: '0 0 1rem 0'
              }}>Week Summary</h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    background: '#dbeafe',
                    borderRadius: '0.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#3b82f6'
                  }}>
                    <svg style={{width: '18px', height: '18px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <circle cx="12" cy="12" r="10"/>
                      <polyline points="12,6 12,12 16,14"/>
                    </svg>
                  </div>
                  <div>
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#64748b',
                      fontWeight: 500
                    }}>Scheduled Hours:</span>
                    <span style={{
                      fontSize: '1rem',
                      fontWeight: 700,
                      color: '#1e293b',
                      marginLeft: '0.5rem'
                    }}>
                      {(() => {
                        const startDate = new Date(selectedWeek);
                        startDate.setDate(startDate.getDate() - startDate.getDay());
                        const endDate = new Date(startDate);
                        endDate.setDate(endDate.getDate() + 6);

                        const hours = scheduleService.calculateHours(
                          staff.id,
                          startDate.toISOString().split('T')[0],
                          endDate.toISOString().split('T')[0]
                        );

                        return `${hours.totalHours}h`;
                      })()}
                    </span>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    background: '#dcfce7',
                    borderRadius: '0.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#16a34a'
                  }}>
                    <svg style={{width: '18px', height: '18px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                      <line x1="16" y1="2" x2="16" y2="6"/>
                      <line x1="8" y1="2" x2="8" y2="6"/>
                      <line x1="3" y1="10" x2="21" y2="10"/>
                    </svg>
                  </div>
                  <div>
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#64748b',
                      fontWeight: 500
                    }}>Working Days:</span>
                    <span style={{
                      fontSize: '1rem',
                      fontWeight: 700,
                      color: '#1e293b',
                      marginLeft: '0.5rem'
                    }}>
                      {Object.values(schedule.regularSchedule).filter(day => day.isWorking).length}
                    </span>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    background: '#fed7aa',
                    borderRadius: '0.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#ea580c'
                  }}>
                    <svg style={{width: '18px', height: '18px'}} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div>
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#64748b',
                      fontWeight: 500
                    }}>Time Off Days:</span>
                    <span style={{
                      fontSize: '1rem',
                      fontWeight: 700,
                      color: '#1e293b',
                      marginLeft: '0.5rem'
                    }}>
                      {(() => {
                        const startDate = new Date(selectedWeek);
                        startDate.setDate(startDate.getDate() - startDate.getDay());
                        const endDate = new Date(startDate);
                        endDate.setDate(endDate.getDate() + 6);

                        return schedule.timeOffRequests.filter(r =>
                          r.status === 'approved' &&
                          ((r.startDate >= startDate.toISOString().split('T')[0] && r.startDate <= endDate.toISOString().split('T')[0]) ||
                           (r.endDate >= startDate.toISOString().split('T')[0] && r.endDate <= endDate.toISOString().split('T')[0]))
                        ).length;
                      })()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StaffSchedule;
