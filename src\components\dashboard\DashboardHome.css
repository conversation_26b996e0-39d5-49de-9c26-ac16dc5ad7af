/* Enhanced Dashboard Home Styles */
.dashboard-home {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-8);
  position: relative;
  overflow-x: hidden;
}

.dashboard-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(217, 70, 239, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Enhanced Welcome Section */
.welcome-section {
  background: var(--gradient-rainbow);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
  color: white;
  padding: var(--space-8);
  border-radius: var(--radius-3xl);
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  overflow: hidden;
  animation: slideInUp 0.8s ease-out;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0.3;
}

.welcome-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--space-3) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.02em;
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.95;
  margin: 0;
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-role-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.role-badge {
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  color: white;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
}

.role-badge:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
}

/* Section Titles */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  position: relative;
  z-index: 1;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
  transition: width var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.stat-card:hover::before {
  width: 8px;
}

.stat-icon {
  width: 72px;
  height: 72px;
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: white;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0);
  transition: transform var(--transition-base);
  border-radius: inherit;
}

.stat-card:hover .stat-icon::before {
  transform: scale(1);
}

.stat-card:hover .stat-icon {
  transform: scale(1.05);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.stat-title {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.change-icon {
  font-size: 0.7rem;
}

.change-text {
  font-size: 0.8rem;
}

/* Quick Actions Section */
.actions-section {
  margin-bottom: 2rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-card:hover {
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.15);
}

.action-icon {
  width: 50px;
  height: 50px;
  background: #f8f9fa;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  transition: transform 0.2s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.action-description {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

/* Permissions Section */
.permissions-section {
  margin-bottom: 2rem;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.permission-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.permission-icon {
  font-size: 1rem;
}

.permission-text {
  font-size: 0.9rem;
  color: #495057;
  font-weight: 500;
}

/* Activity Section */
.activity-section {
  margin-bottom: 2rem;
}

.activity-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 0.9rem;
  color: #495057;
  margin: 0 0 0.25rem 0;
}

.activity-time {
  font-size: 0.8rem;
  color: #6c757d;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Content Grid */
.main-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.view-all-btn {
  background: none;
  border: none;
  color: #3498db;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: #f8f9fa;
  color: #2980b9;
}

/* Appointments List */
.appointments-list {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.appointment-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.appointment-item:last-child {
  border-bottom: none;
}

.appointment-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.appointment-time .time {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.appointment-time .duration {
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.appointment-details {
  flex: 1;
}

.customer-name {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.service-name {
  font-size: 0.9rem;
  color: #495057;
  margin: 0 0 0.25rem 0;
}

.staff-name {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
}

.empty-state {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* Charts Styles */
.dashboard-charts {
  margin-bottom: 2rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-card-wide {
  grid-column: span 2;
}

.chart-header {
  margin-bottom: 1.5rem;
}

.chart-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.chart-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.charts-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-card-wide {
    grid-column: span 1;
  }
}

/* Add missing animations */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Dark Mode Support */
[data-theme="dark"] .dashboard-home::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(217, 70, 239, 0.05) 0%, transparent 50%);
}

[data-theme="dark"] .stat-card {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .welcome-section {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .dashboard-home::before,
  .welcome-section,
  .stat-card,
  .stat-icon {
    animation: none;
  }

  .stat-card:hover,
  .stat-icon:hover {
    transform: none;
  }

  .welcome-section {
    background: var(--gradient-primary);
  }
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .stats-grid,
  .actions-grid,
  .permissions-grid {
    grid-template-columns: 1fr;
  }

  .main-content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }

  .stat-card,
  .action-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .view-all-btn {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .welcome-section {
    padding: 1.5rem;
  }

  .welcome-title {
    font-size: 1.3rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .chart-container {
    height: 200px;
  }

  .appointment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .appointment-time {
    align-self: stretch;
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }
}
