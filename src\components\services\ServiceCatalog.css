/* Service Catalog Styles - Package-like UI */
.service-catalog {
  width: 100%;
}

/* Services Header */
.services-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.title-icon {
  font-size: var(--text-2xl);
}

.section-subtitle {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0;
}



.filter-section {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}



.filter-select {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: white;
  cursor: pointer;
  transition: all var(--transition-base);
}

.filter-select:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}



/* Results Info */
.results-info {
  margin-bottom: var(--space-4);
}

.results-count {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* Services Container */
.services-container {
  width: 100%;
}



/* Services Grid - Package-like Layout */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}





/* Service Card */
.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: var(--space-6);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8b5cf6, #3b82f6, #ec4899);
}

.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(139, 92, 246, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.service-card:hover::after {
  opacity: 1;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.service-name {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.service-description {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.6;
  margin: 0 0 var(--space-4) 0;
}



.service-name {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-1) 0;
}

.service-id {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin: 0;
}

/* Status Badges */
.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background: var(--color-green-100);
  color: var(--color-green-700);
}

.status-inactive {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.status-discontinued {
  background: var(--color-red-100);
  color: var(--color-red-700);
}

/* Service Details - Package-like Layout */
.service-details {
  margin-bottom: var(--space-4);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-gray-100);
  font-size: var(--text-sm);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: var(--color-gray-600);
  font-weight: var(--font-medium);
}

.detail-value {
  color: var(--color-gray-900);
  font-weight: var(--font-semibold);
}

/* Category Badge */
.category-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.category-icon {
  font-size: var(--text-sm);
}

/* Service Requirements - Package-like Layout */
.service-requirements {
  margin-bottom: var(--space-4);
}

.service-requirements h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--color-gray-700);
  margin: 0 0 var(--space-2) 0;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirement-item {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-gray-100);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.requirement-item:last-child {
  border-bottom: none;
}

/* Service Actions - Package-like Layout */
.service-actions {
  display: flex;
  gap: var(--space-2);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-gray-200);
}

/* No Services State */
.no-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 1400px;
  margin: 0 auto;
}

.no-services-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.no-services h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.no-services p {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0;
}

/* List View Specific Styles */
.services-list .service-card {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-4);
}

.services-list .service-header {
  margin-bottom: 0;
  flex-shrink: 0;
}

.services-list .service-content {
  flex: 1;
  margin-bottom: 0;
}

.services-list .service-details {
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.services-list .service-actions {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
  flex-shrink: 0;
}



/* Responsive Design */
@media (max-width: 768px) {
  .service-controls {
    flex-direction: column;
    align-items: stretch;
    padding: var(--space-4);
  }

  .search-section {
    min-width: auto;
  }

  .filter-section {
    justify-content: space-between;
  }

  .services-grid {
    grid-template-columns: 1fr;
    padding: 0 var(--space-4);
  }

  .service-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: flex-start;
  }

  .service-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .service-controls {
    padding: var(--space-3);
  }

  .services-grid {
    padding: 0 var(--space-3);
  }

  .service-card {
    padding: var(--space-4);
  }

  .service-avatar {
    width: 60px;
    height: 60px;
  }

  .avatar-placeholder {
    font-size: var(--text-lg);
  }

  .service-actions {
    flex-direction: column;
  }

  .filter-section {
    flex-direction: column;
    gap: var(--space-2);
  }
}
