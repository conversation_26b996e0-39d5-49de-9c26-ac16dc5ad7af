import React, { useState, useEffect } from 'react';
import appointmentService from '../../services/appointmentService';
import customerService from '../../services/customerService';
import TimeSlotPicker from './TimeSlotPicker';
import toast from 'react-hot-toast';
import './AppointmentForm.css';

const AppointmentForm = ({ 
  appointment = null, 
  onSave, 
  onCancel, 
  preselectedDate = null,
  preselectedCustomer = null 
}) => {
  const [formData, setFormData] = useState({
    customerId: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    serviceId: '',
    staffId: '',
    date: '',
    time: '',
    notes: ''
  });
  
  const [customers, setCustomers] = useState([]);
  const [services, setServices] = useState([]);
  const [staffMembers, setStaffMembers] = useState([]);
  const [availableStaff, setAvailableStaff] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showNewCustomer, setShowNewCustomer] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (appointment) {
      const appointmentDate = new Date(appointment.date);
      setFormData({
        customerId: appointment.customerId,
        customerName: appointment.customerName,
        customerPhone: appointment.customerPhone || '',
        customerEmail: appointment.customerEmail || '',
        serviceId: appointment.serviceId,
        staffId: appointment.staffId,
        date: appointmentDate.toISOString().split('T')[0],
        time: appointment.date,
        notes: appointment.notes || ''
      });
    } else {
      // Set preselected values
      if (preselectedDate) {
        setFormData(prev => ({
          ...prev,
          date: preselectedDate.toISOString().split('T')[0]
        }));
      }
      if (preselectedCustomer) {
        setFormData(prev => ({
          ...prev,
          customerId: preselectedCustomer.id,
          customerName: preselectedCustomer.name,
          customerPhone: preselectedCustomer.phone || '',
          customerEmail: preselectedCustomer.email || ''
        }));
      }
    }
  }, [appointment, preselectedDate, preselectedCustomer]);

  useEffect(() => {
    if (formData.serviceId) {
      const staffForService = appointmentService.getStaffForService(formData.serviceId);
      setAvailableStaff(staffForService);
      
      // Reset staff selection if current staff can't perform the service
      if (formData.staffId && !staffForService.find(s => s.id === formData.staffId)) {
        setFormData(prev => ({ ...prev, staffId: '' }));
      }
    } else {
      setAvailableStaff(staffMembers);
    }
  }, [formData.serviceId, staffMembers]);

  const loadInitialData = async () => {
    try {
      const [customersData, servicesData, staffData] = await Promise.all([
        customerService.getAllCustomers(),
        appointmentService.getServices(),
        appointmentService.getStaffMembers()
      ]);
      
      setCustomers(customersData);
      setServices(servicesData);
      setStaffMembers(staffData);
      setAvailableStaff(staffData);
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('Failed to load form data');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Handle customer selection
    if (name === 'customerId') {
      if (value === 'new') {
        setShowNewCustomer(true);
        setFormData(prev => ({
          ...prev,
          customerId: '',
          customerName: '',
          customerPhone: '',
          customerEmail: ''
        }));
      } else {
        setShowNewCustomer(false);
        const customer = customers.find(c => c.id === value);
        if (customer) {
          const fullName = `${customer.firstName} ${customer.lastName}`.trim();
          setFormData(prev => ({
            ...prev,
            customerName: fullName,
            customerPhone: customer.phone || '',
            customerEmail: customer.email || ''
          }));
        }
      }
    }
  };

  const handleTimeSelect = (time) => {
    setFormData(prev => ({ ...prev, time }));
    if (errors.time) {
      setErrors(prev => ({ ...prev, time: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.serviceId) {
      newErrors.serviceId = 'Service selection is required';
    }

    if (!formData.staffId) {
      newErrors.staffId = 'Staff selection is required';
    }

    if (!formData.date) {
      newErrors.date = 'Date is required';
    } else {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        newErrors.date = 'Cannot book appointments in the past';
      }
    }

    if (!formData.time) {
      newErrors.time = 'Time selection is required';
    }

    if (showNewCustomer && formData.customerPhone && !/^\d{10,}$/.test(formData.customerPhone.replace(/\D/g, ''))) {
      newErrors.customerPhone = 'Please enter a valid phone number';
    }

    if (showNewCustomer && formData.customerEmail && !/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);
    
    try {
      let customerId = formData.customerId;
      
      // Create new customer if needed
      if (showNewCustomer || !customerId) {
        const customerData = {
          firstName: formData.customerName.split(' ')[0] || formData.customerName,
          lastName: formData.customerName.split(' ').slice(1).join(' ') || '',
          phone: formData.customerPhone,
          email: formData.customerEmail
        };

        const result = customerService.addCustomer(customerData);
        if (!result.success) {
          throw new Error(result.error || 'Failed to create customer');
        }
        customerId = result.customer.id;
      }

      const appointmentData = {
        customerId,
        customerName: formData.customerName,
        customerPhone: formData.customerPhone,
        customerEmail: formData.customerEmail,
        serviceId: formData.serviceId,
        staffId: formData.staffId,
        date: formData.time, // This should be the full datetime from TimeSlotPicker
        notes: formData.notes
      };

      let savedAppointment;
      if (appointment) {
        savedAppointment = await appointmentService.updateAppointment(appointment.id, appointmentData);
        toast.success('Appointment updated successfully!');
      } else {
        savedAppointment = await appointmentService.createAppointment(appointmentData);
        toast.success('Appointment booked successfully!');
      }

      onSave && onSave(savedAppointment);
    } catch (error) {
      console.error('Error saving appointment:', error);
      toast.error(error.message || 'Failed to save appointment');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedService = () => {
    return services.find(s => s.id === formData.serviceId);
  };

  const getSelectedStaff = () => {
    return staffMembers.find(s => s.id === formData.staffId);
  };

  return (
    <div className="appointment-form">
      <div className="form-header">
        <h2>{appointment ? 'Edit Appointment' : 'Book New Appointment'}</h2>
      </div>

      <form onSubmit={handleSubmit} className="appointment-form-content">
        <div className="form-section">
          <h3>Customer Information</h3>
          
          <div className="form-group">
            <label htmlFor="customerId">Select Customer</label>
            <select
              id="customerId"
              name="customerId"
              value={formData.customerId}
              onChange={handleInputChange}
              className={errors.customerId ? 'error' : ''}
            >
              <option value="">Select a customer...</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {`${customer.firstName} ${customer.lastName}`.trim()} - {customer.phone}
                </option>
              ))}
              <option value="new">+ Add New Customer</option>
            </select>
            {errors.customerId && <span className="error-message">{errors.customerId}</span>}
          </div>

          {(showNewCustomer || !formData.customerId) && (
            <>
              <div className="form-group">
                <label htmlFor="customerName">Customer Name *</label>
                <input
                  type="text"
                  id="customerName"
                  name="customerName"
                  value={formData.customerName}
                  onChange={handleInputChange}
                  className={errors.customerName ? 'error' : ''}
                  placeholder="Enter customer name"
                />
                {errors.customerName && <span className="error-message">{errors.customerName}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="customerPhone">Phone Number</label>
                  <input
                    type="tel"
                    id="customerPhone"
                    name="customerPhone"
                    value={formData.customerPhone}
                    onChange={handleInputChange}
                    className={errors.customerPhone ? 'error' : ''}
                    placeholder="(*************"
                  />
                  {errors.customerPhone && <span className="error-message">{errors.customerPhone}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="customerEmail">Email Address</label>
                  <input
                    type="email"
                    id="customerEmail"
                    name="customerEmail"
                    value={formData.customerEmail}
                    onChange={handleInputChange}
                    className={errors.customerEmail ? 'error' : ''}
                    placeholder="<EMAIL>"
                  />
                  {errors.customerEmail && <span className="error-message">{errors.customerEmail}</span>}
                </div>
              </div>
            </>
          )}
        </div>

        <div className="form-section">
          <h3>Service & Staff</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="serviceId">Service *</label>
              <select
                id="serviceId"
                name="serviceId"
                value={formData.serviceId}
                onChange={handleInputChange}
                className={errors.serviceId ? 'error' : ''}
              >
                <option value="">Select a service...</option>
                {services.map(service => (
                  <option key={service.id} value={service.id}>
                    {service.name} - ${service.price} ({service.duration} min)
                  </option>
                ))}
              </select>
              {errors.serviceId && <span className="error-message">{errors.serviceId}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="staffId">Staff Member *</label>
              <select
                id="staffId"
                name="staffId"
                value={formData.staffId}
                onChange={handleInputChange}
                className={errors.staffId ? 'error' : ''}
                disabled={!formData.serviceId}
              >
                <option value="">Select staff member...</option>
                {availableStaff.map(staff => (
                  <option key={staff.id} value={staff.id}>
                    {staff.name}
                  </option>
                ))}
              </select>
              {errors.staffId && <span className="error-message">{errors.staffId}</span>}
            </div>
          </div>

          {getSelectedService() && (
            <div className="service-info">
              <p><strong>Service:</strong> {getSelectedService().name}</p>
              <p><strong>Duration:</strong> {getSelectedService().duration} minutes</p>
              <p><strong>Price:</strong> ${getSelectedService().price}</p>
            </div>
          )}
        </div>

        <div className="form-section">
          <h3>Date & Time</h3>
          
          <div className="form-group">
            <label htmlFor="date">Date *</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className={errors.date ? 'error' : ''}
              min={new Date().toISOString().split('T')[0]}
            />
            {errors.date && <span className="error-message">{errors.date}</span>}
          </div>

          {formData.date && formData.staffId && (
            <div className="time-slot-section">
              <TimeSlotPicker
                selectedDate={formData.date}
                selectedStaff={formData.staffId}
                selectedService={formData.serviceId}
                selectedTime={formData.time}
                onTimeSelect={handleTimeSelect}
                excludeAppointmentId={appointment?.id}
              />
              {errors.time && <span className="error-message">{errors.time}</span>}
            </div>
          )}
        </div>

        <div className="form-section">
          <h3>Additional Notes</h3>
          <div className="form-group">
            <label htmlFor="notes">Notes (Optional)</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              placeholder="Any special requests or notes..."
              rows="3"
            />
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : (appointment ? 'Update Appointment' : 'Book Appointment')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AppointmentForm;
