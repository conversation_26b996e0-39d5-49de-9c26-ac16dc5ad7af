// Authentication service for salon management system
import { USER_ROLES } from '../contexts/AuthContext';

class AuthService {
  constructor() {
    this.TOKEN_KEY = 'salon_auth_token';
    this.USER_DATA_KEY = 'salon_user_data';
    this.REMEMBER_ME_KEY = 'salon_remember_me';
  }

  // Get stored token
  getToken() {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  // Get stored user data
  getUserData() {
    try {
      const userData = localStorage.getItem(this.USER_DATA_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getToken();
    const userData = this.getUserData();
    return !!(token && userData);
  }

  // Validate token (basic validation for demo)
  isTokenValid() {
    const token = this.getToken();
    if (!token) return false;

    try {
      const decoded = JSON.parse(atob(token));
      const now = Date.now();
      const tokenAge = now - decoded.timestamp;
      
      // Token expires after 24 hours (86400000 ms)
      const TOKEN_EXPIRY = 24 * 60 * 60 * 1000;
      
      return tokenAge < TOKEN_EXPIRY;
    } catch (error) {
      console.error('Error validating token:', error);
      return false;
    }
  }

  // Store authentication data
  storeAuthData(token, userData, rememberMe = false) {
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.USER_DATA_KEY, JSON.stringify(userData));
    
    if (rememberMe) {
      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');
    }
  }

  // Clear authentication data
  clearAuthData() {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_DATA_KEY);
    localStorage.removeItem(this.REMEMBER_ME_KEY);
  }

  // Check if remember me is enabled
  isRememberMeEnabled() {
    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';
  }

  // Role-based access control methods
  hasRole(requiredRole) {
    const userData = this.getUserData();
    return userData?.role === requiredRole;
  }

  hasAnyRole(roles) {
    const userData = this.getUserData();
    return roles.includes(userData?.role);
  }

  isAdmin() {
    return this.hasRole(USER_ROLES.ADMIN);
  }

  isStaff() {
    return this.hasRole(USER_ROLES.STAFF);
  }

  isReceptionist() {
    return this.hasRole(USER_ROLES.RECEPTIONIST);
  }

  isStaffOrAdmin() {
    return this.hasAnyRole([USER_ROLES.STAFF, USER_ROLES.ADMIN]);
  }

  canAccessCustomers() {
    return this.hasAnyRole([USER_ROLES.ADMIN, USER_ROLES.STAFF, USER_ROLES.RECEPTIONIST]);
  }

  canAccessAppointments() {
    return this.hasAnyRole([USER_ROLES.ADMIN, USER_ROLES.STAFF, USER_ROLES.RECEPTIONIST]);
  }

  canAccessServices() {
    return this.hasAnyRole([USER_ROLES.ADMIN, USER_ROLES.STAFF]);
  }

  canAccessStaff() {
    return this.hasRole(USER_ROLES.ADMIN);
  }

  canAccessBilling() {
    return this.hasAnyRole([USER_ROLES.ADMIN, USER_ROLES.RECEPTIONIST]);
  }

  canAccessInventory() {
    return this.hasAnyRole([USER_ROLES.ADMIN, USER_ROLES.STAFF]);
  }

  canAccessReports() {
    return this.hasRole(USER_ROLES.ADMIN);
  }

  canManageUsers() {
    return this.hasRole(USER_ROLES.ADMIN);
  }

  canEditProfile() {
    return this.isAuthenticated();
  }

  // Get user permissions based on role
  getUserPermissions() {
    const userData = this.getUserData();
    if (!userData) return [];

    const permissions = [];

    switch (userData.role) {
      case USER_ROLES.ADMIN:
        permissions.push(
          'view_dashboard',
          'manage_customers',
          'manage_appointments',
          'manage_services',
          'manage_staff',
          'manage_billing',
          'manage_inventory',
          'view_reports',
          'manage_users',
          'edit_profile'
        );
        break;

      case USER_ROLES.STAFF:
        permissions.push(
          'view_dashboard',
          'manage_customers',
          'manage_appointments',
          'manage_services',
          'manage_inventory',
          'edit_profile'
        );
        break;

      case USER_ROLES.RECEPTIONIST:
        permissions.push(
          'view_dashboard',
          'manage_customers',
          'manage_appointments',
          'manage_billing',
          'edit_profile'
        );
        break;

      default:
        break;
    }

    return permissions;
  }

  // Check if user has specific permission
  hasPermission(permission) {
    const permissions = this.getUserPermissions();
    return permissions.includes(permission);
  }

  // Get user display info
  getUserDisplayInfo() {
    const userData = this.getUserData();
    if (!userData) return null;

    return {
      name: userData.name,
      email: userData.email,
      role: userData.role,
      avatar: userData.avatar,
      initials: this.getInitials(userData.name)
    };
  }

  // Get user initials for avatar
  getInitials(name) {
    if (!name) return 'U';
    
    const names = name.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  }

  // Format role for display
  formatRole(role) {
    switch (role) {
      case USER_ROLES.ADMIN:
        return 'Administrator';
      case USER_ROLES.STAFF:
        return 'Staff Member';
      case USER_ROLES.RECEPTIONIST:
        return 'Receptionist';
      default:
        return 'User';
    }
  }

  // Get role color for UI
  getRoleColor(role) {
    switch (role) {
      case USER_ROLES.ADMIN:
        return '#dc2626'; // red
      case USER_ROLES.STAFF:
        return '#2563eb'; // blue
      case USER_ROLES.RECEPTIONIST:
        return '#059669'; // green
      default:
        return '#6b7280'; // gray
    }
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
