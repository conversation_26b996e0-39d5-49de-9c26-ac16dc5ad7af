import React, { useState, useEffect } from 'react';
import appointmentService, { APPOINTMENT_STATUS } from '../../services/appointmentService';
import toast from 'react-hot-toast';
import './AppointmentReminders.css';

const AppointmentReminders = ({ onAppointmentSelect }) => {
  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [reminderSettings, setReminderSettings] = useState({
    enabled: true,
    reminderTime: 24, // hours before appointment
    autoReminders: true
  });

  useEffect(() => {
    loadReminders();
    loadReminderSettings();
    
    // Set up interval to check for reminders every minute
    const interval = setInterval(checkForReminders, 60000);
    
    return () => clearInterval(interval);
  }, []);

  const loadReminders = () => {
    setLoading(true);
    try {
      const now = new Date();
      const reminderWindow = new Date(now.getTime() + reminderSettings.reminderTime * 60 * 60 * 1000);
      
      const allAppointments = appointmentService.getAllAppointments();
      
      // Filter appointments that need reminders
      const upcomingAppointments = allAppointments.filter(appointment => {
        const appointmentDate = new Date(appointment.date);
        return (
          appointmentDate > now &&
          appointmentDate <= reminderWindow &&
          [APPOINTMENT_STATUS.SCHEDULED, APPOINTMENT_STATUS.CONFIRMED].includes(appointment.status)
        );
      });

      // Sort by appointment date
      upcomingAppointments.sort((a, b) => new Date(a.date) - new Date(b.date));
      
      setReminders(upcomingAppointments);
    } catch (error) {
      console.error('Error loading reminders:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadReminderSettings = () => {
    try {
      const saved = localStorage.getItem('salon_reminder_settings');
      if (saved) {
        setReminderSettings(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading reminder settings:', error);
    }
  };

  const saveReminderSettings = (settings) => {
    try {
      localStorage.setItem('salon_reminder_settings', JSON.stringify(settings));
      setReminderSettings(settings);
      loadReminders(); // Reload reminders with new settings
    } catch (error) {
      console.error('Error saving reminder settings:', error);
    }
  };

  const checkForReminders = () => {
    if (!reminderSettings.enabled || !reminderSettings.autoReminders) return;
    
    const now = new Date();
    const reminderThreshold = new Date(now.getTime() + reminderSettings.reminderTime * 60 * 60 * 1000);
    
    reminders.forEach(appointment => {
      const appointmentDate = new Date(appointment.date);
      const timeDiff = appointmentDate.getTime() - now.getTime();
      const hoursUntil = timeDiff / (1000 * 60 * 60);
      
      // Show reminder if appointment is within the reminder window
      if (hoursUntil <= reminderSettings.reminderTime && hoursUntil > 0) {
        const reminderKey = `reminder_${appointment.id}_${appointmentDate.getTime()}`;
        
        // Check if we've already shown this reminder
        if (!localStorage.getItem(reminderKey)) {
          showReminderNotification(appointment, hoursUntil);
          localStorage.setItem(reminderKey, 'shown');
        }
      }
    });
  };

  const showReminderNotification = (appointment, hoursUntil) => {
    const timeText = hoursUntil < 1 
      ? `${Math.round(hoursUntil * 60)} minutes`
      : `${Math.round(hoursUntil)} hours`;
    
    toast.success(
      `Reminder: ${appointment.customerName} has an appointment in ${timeText}`,
      {
        duration: 8000,
        icon: '⏰',
      }
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getTimeUntilAppointment = (dateString) => {
    const now = new Date();
    const appointmentDate = new Date(dateString);
    const timeDiff = appointmentDate.getTime() - now.getTime();
    
    if (timeDiff <= 0) return 'Past due';
    
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours === 0) {
      return `${minutes} min`;
    } else if (hours < 24) {
      return `${hours}h ${minutes}m`;
    } else {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days}d ${remainingHours}h`;
    }
  };

  const getUrgencyLevel = (dateString) => {
    const now = new Date();
    const appointmentDate = new Date(dateString);
    const hoursUntil = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntil <= 1) return 'urgent';
    if (hoursUntil <= 4) return 'warning';
    if (hoursUntil <= 24) return 'info';
    return 'normal';
  };

  const dismissReminder = (appointmentId) => {
    setReminders(prev => prev.filter(reminder => reminder.id !== appointmentId));
  };

  const sendManualReminder = (appointment) => {
    // In a real app, this would send an SMS/email
    toast.success(`Reminder sent to ${appointment.customerName}`, {
      icon: '📱',
    });
  };

  const handleSettingsChange = (key, value) => {
    const newSettings = { ...reminderSettings, [key]: value };
    saveReminderSettings(newSettings);
  };

  if (loading) {
    return (
      <div className="appointment-reminders">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading reminders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-reminders">
      <div className="reminders-header">
        <h3>Appointment Reminders</h3>
        <div className="reminder-count">
          {reminders.length} upcoming appointment{reminders.length !== 1 ? 's' : ''}
        </div>
      </div>

      <div className="reminder-settings">
        <div className="setting-item">
          <label>
            <input
              type="checkbox"
              checked={reminderSettings.enabled}
              onChange={(e) => handleSettingsChange('enabled', e.target.checked)}
            />
            Enable reminders
          </label>
        </div>
        
        <div className="setting-item">
          <label>
            Remind me
            <select
              value={reminderSettings.reminderTime}
              onChange={(e) => handleSettingsChange('reminderTime', parseInt(e.target.value))}
              disabled={!reminderSettings.enabled}
            >
              <option value={1}>1 hour before</option>
              <option value={2}>2 hours before</option>
              <option value={4}>4 hours before</option>
              <option value={24}>24 hours before</option>
              <option value={48}>48 hours before</option>
            </select>
          </label>
        </div>
        
        <div className="setting-item">
          <label>
            <input
              type="checkbox"
              checked={reminderSettings.autoReminders}
              onChange={(e) => handleSettingsChange('autoReminders', e.target.checked)}
              disabled={!reminderSettings.enabled}
            />
            Auto-show reminders
          </label>
        </div>
      </div>

      <div className="reminders-list">
        {reminders.length === 0 ? (
          <div className="no-reminders">
            <p>No upcoming appointments requiring reminders.</p>
          </div>
        ) : (
          reminders.map(appointment => (
            <div
              key={appointment.id}
              className={`reminder-item ${getUrgencyLevel(appointment.date)}`}
              onClick={() => onAppointmentSelect && onAppointmentSelect(appointment)}
            >
              <div className="reminder-content">
                <div className="reminder-main">
                  <div className="customer-name">{appointment.customerName}</div>
                  <div className="appointment-details">
                    {appointment.serviceName} with {appointment.staffName}
                  </div>
                  <div className="appointment-time">
                    {formatDate(appointment.date)} at {formatTime(appointment.date)}
                  </div>
                </div>
                
                <div className="reminder-meta">
                  <div className="time-until">
                    {getTimeUntilAppointment(appointment.date)}
                  </div>
                  <div className="urgency-indicator">
                    {getUrgencyLevel(appointment.date) === 'urgent' && '🔴'}
                    {getUrgencyLevel(appointment.date) === 'warning' && '🟡'}
                    {getUrgencyLevel(appointment.date) === 'info' && '🔵'}
                  </div>
                </div>
              </div>
              
              <div className="reminder-actions">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    sendManualReminder(appointment);
                  }}
                  className="action-btn send-reminder"
                  title="Send reminder to customer"
                >
                  📱
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    dismissReminder(appointment.id);
                  }}
                  className="action-btn dismiss"
                  title="Dismiss reminder"
                >
                  ✕
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {reminders.length > 0 && (
        <div className="reminders-footer">
          <button
            onClick={() => {
              reminders.forEach(appointment => sendManualReminder(appointment));
            }}
            className="btn btn-primary btn-sm"
          >
            Send All Reminders
          </button>
          <button
            onClick={() => setReminders([])}
            className="btn btn-secondary btn-sm"
          >
            Dismiss All
          </button>
        </div>
      )}
    </div>
  );
};

export default AppointmentReminders;
