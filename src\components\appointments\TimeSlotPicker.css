/* Time Slot Picker Styles */
.time-slot-picker {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 1rem;
}

.slot-picker-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.slot-picker-header h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
}

.selected-date {
  color: #007bff;
  font-weight: 500;
  margin: 0.5rem 0;
}

.service-duration {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

/* No Selection State */
.no-selection {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.no-selection p {
  margin: 0;
  font-size: 1rem;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Slots Available */
.no-slots {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  border: 2px dashed #e9ecef;
}

.no-slots p {
  margin: 0.5rem 0;
  color: #6c757d;
}

/* Time Slots Container */
.time-slots-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-period {
  margin-bottom: 2rem;
}

.time-period:last-child {
  margin-bottom: 0;
}

.period-title {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

/* Time Slots Grid */
.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 0.75rem;
}

.time-slot {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.time-slot:hover {
  background: #e3f2fd;
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.time-slot.selected {
  background: #007bff;
  border-color: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.slot-time {
  font-weight: 600;
  font-size: 0.95rem;
}

.slot-end-time {
  display: block;
  font-size: 0.8rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Footer Information */
.slot-picker-footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.slot-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-icon {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .time-slot-picker {
    padding: 1rem;
  }
  
  .slot-picker-header h3 {
    font-size: 1.1rem;
  }
  
  .time-slots-container {
    padding: 1rem;
  }
  
  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .time-slot {
    padding: 0.5rem;
  }
  
  .slot-time {
    font-size: 0.9rem;
  }
  
  .slot-end-time {
    font-size: 0.75rem;
  }
  
  .period-title {
    font-size: 1rem;
  }
  
  .slot-info {
    gap: 0.75rem;
  }
  
  .info-item {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .time-slot-picker {
    padding: 0.75rem;
  }
  
  .slot-picker-header {
    margin-bottom: 1rem;
  }
  
  .slot-picker-header h3 {
    font-size: 1rem;
  }
  
  .selected-date {
    font-size: 0.9rem;
  }
  
  .service-duration {
    font-size: 0.8rem;
  }
  
  .time-slots-container {
    padding: 0.75rem;
  }
  
  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }
  
  .time-slot {
    padding: 0.5rem 0.25rem;
  }
  
  .slot-time {
    font-size: 0.8rem;
  }
  
  .slot-end-time {
    font-size: 0.7rem;
  }
  
  .period-title {
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
  }
  
  .slot-picker-footer {
    margin-top: 1rem;
    padding-top: 1rem;
  }
  
  .info-item {
    font-size: 0.8rem;
  }
  
  .no-selection,
  .no-slots {
    padding: 1.5rem;
  }
  
  .no-selection p,
  .no-slots p {
    font-size: 0.9rem;
  }
}

/* Animation for slot selection */
@keyframes slot-select {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.time-slot.selected {
  animation: slot-select 0.3s ease-in-out;
}

/* Accessibility improvements */
.time-slot:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.time-slot:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .time-slot {
    border-width: 3px;
  }
  
  .time-slot:hover {
    border-width: 3px;
  }
  
  .time-slot.selected {
    border-width: 3px;
  }
}
