import { useState } from 'react'
import './ContactUs.css'

function ContactUs() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    setTimeout(() => {
      alert('Thank you for your message! We\'ll get back to you within 24 hours.')
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        service: '',
        message: ''
      })
      setIsSubmitting(false)
    }, 1000)
  }

  return (
    <>
      {/* Contact Hero Section */}
      <section className="contact-hero-section">
        <div className="contact-hero-background">
          <div className="contact-hero-overlay"></div>
        </div>
        <div className="contact-hero-content">
          <div className="contact-hero-text">
            <span className="contact-hero-badge">✨ Get In Touch</span>
            <h1 className="contact-hero-title">Contact Us</h1>
            <p className="contact-hero-subtitle">
              Ready to transform your look? We'd love to hear from you and help you achieve your beauty goals.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="contact-info-section">
        <div className="contact-info-container">
          <div className="section-header">
            <span className="section-badge">Contact Information</span>
            <h2 className="section-title">Visit Our Salon</h2>
            <p className="section-subtitle">
              Find us at our convenient location or reach out through any of these channels
            </p>
          </div>
          <div className="contact-info-grid">
            <div className="contact-info-card">
              <div className="contact-info-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
              <h3 className="contact-info-title">Visit Us</h3>
              <div className="contact-info-details">
                <p>123 Beauty Street</p>
                <p>New York, NY 10001</p>
                <p>United States</p>
              </div>
            </div>
            <div className="contact-info-card">
              <div className="contact-info-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                </svg>
              </div>
              <h3 className="contact-info-title">Call Us</h3>
              <div className="contact-info-details">
                <p>(555) 123-4567</p>
                <p>(555) 987-6543</p>
                <p>Mon-Sat: 9AM-8PM</p>
              </div>
            </div>
            <div className="contact-info-card">
              <div className="contact-info-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </div>
              <h3 className="contact-info-title">Email Us</h3>
              <div className="contact-info-details">
                <p><EMAIL></p>
                <p><EMAIL></p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section">
        <div className="contact-form-container">
          <div className="contact-form-content">
            <div className="contact-form-text">
              <div className="section-header">
                <span className="section-badge">Send Message</span>
                <h2 className="section-title">Get In Touch</h2>
                <p className="section-subtitle">
                  Fill out the form below and our team will get back to you within 24 hours.
                </p>
              </div>
              <div className="contact-features">
                <div className="contact-feature">
                  <div className="feature-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <div className="feature-text">
                    <h4>Quick Response</h4>
                    <p>We respond to all inquiries within 24 hours</p>
                  </div>
                </div>
                <div className="contact-feature">
                  <div className="feature-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <div className="feature-text">
                    <h4>Expert Consultation</h4>
                    <p>Free consultation with our beauty experts</p>
                  </div>
                </div>
                <div className="contact-feature">
                  <div className="feature-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div className="feature-text">
                    <h4>Personalized Service</h4>
                    <p>Tailored recommendations for your needs</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="contact-form-card">
              <form className="contact-form" onSubmit={handleSubmit}>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">First Name *</label>
                    <input
                      type="text"
                      name="firstName"
                      className="form-input"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder="Enter your first name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Last Name *</label>
                    <input
                      type="text"
                      name="lastName"
                      className="form-input"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder="Enter your last name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      className="form-input"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      className="form-input"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Enter your phone number"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Service Interest</label>
                  <select
                    name="service"
                    className="form-select"
                    value={formData.service}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  >
                    <option value="">Select a service</option>
                    <option value="hair-styling">Hair Styling</option>
                    <option value="hair-coloring">Hair Coloring</option>
                    <option value="facial-treatments">Facial Treatments</option>
                    <option value="nail-care">Nail Care</option>
                    <option value="bridal-packages">Bridal Packages</option>
                    <option value="makeup-services">Makeup Services</option>
                    <option value="consultation">General Consultation</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Message *</label>
                  <textarea
                    name="message"
                    className="form-textarea"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Tell us about your beauty goals or any questions you have..."
                    rows="5"
                    required
                    disabled={isSubmitting}
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="form-submit-btn"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="map-section">
        <div className="map-container">
          <div className="map-placeholder">
            <div className="map-content">
              <div className="map-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
              <h3>Find Us Here</h3>
              <p>123 Beauty Street, New York, NY 10001</p>
              <a href="#" className="map-link">Get Directions</a>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default ContactUs
