/* Service Form Styles - Modern UI */
.service-form {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Form Header */
.form-header {
  margin-bottom: var(--space-8);
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  padding: var(--space-8);
  border-radius: 24px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.form-title {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 var(--space-3) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
  letter-spacing: -0.02em;
}

.form-title span {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
}

.form-subtitle {
  color: #64748b;
  font-size: var(--text-lg);
  margin: 0;
  font-weight: 500;
  opacity: 0.8;
}

/* Form Content */
.service-form-content {
  width: 100%;
}

.form-grid {
  display: grid;
  gap: var(--space-8);
}

/* Form Sections */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: var(--space-8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 var(--space-6) 0;
  padding-bottom: var(--space-3);
  border-bottom: 2px solid rgba(203, 213, 225, 0.3);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 1px;
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

/* Form Inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid rgba(203, 213, 225, 0.3);
  border-radius: 16px;
  font-size: var(--text-base);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.1),
    0 10px 15px -3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 1);
}

.form-input-error {
  border-color: #ef4444;
  background: rgba(254, 242, 242, 0.9);
}

.form-input-error:focus {
  border-color: #ef4444;
  box-shadow:
    0 0 0 4px rgba(239, 68, 68, 0.1),
    0 10px 15px -3px rgba(239, 68, 68, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #94a3b8;
  font-weight: 500;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  display: block;
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
}

/* Error Messages */
.error-message {
  display: block;
  font-size: var(--text-xs);
  color: var(--color-red-600);
  margin-top: var(--space-1);
  font-weight: var(--font-medium);
}

/* Requirements Section */
.requirements-input {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.requirements-input .form-input {
  flex: 1;
}

.requirements-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.requirement-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.requirement-remove {
  background: none;
  border: none;
  color: var(--color-primary-600);
  cursor: pointer;
  font-size: var(--text-lg);
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-base);
}

.requirement-remove:hover {
  background: var(--color-primary-200);
  color: var(--color-primary-800);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 2px solid var(--color-gray-200);
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-form {
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-section {
    padding: var(--space-4);
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .requirements-input {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .form-header {
    margin-bottom: var(--space-4);
  }

  .form-title {
    font-size: var(--text-xl);
    flex-direction: column;
    gap: var(--space-2);
  }

  .form-section {
    padding: var(--space-3);
  }

  .form-grid {
    gap: var(--space-4);
  }
}

/* Animation for form sections */
.form-section {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for better accessibility */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  transform: translateY(-1px);
}

/* Hover effects for interactive elements */
.requirement-tag {
  transition: all var(--transition-base);
}

.requirement-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Loading state for form submission */
.form-actions .btn:disabled {
  position: relative;
  overflow: hidden;
}

.form-actions .btn:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
