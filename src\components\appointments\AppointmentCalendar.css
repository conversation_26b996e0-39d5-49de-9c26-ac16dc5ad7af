/* Appointment Calendar Styles */
.appointment-calendar {
  padding: 2rem;
}

/* Calendar Controls */
.calendar-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #495057;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.calendar-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  min-width: 200px;
  text-align: center;
}

.calendar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-mode-selector {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.25rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.view-btn:hover {
  color: #495057;
}

.view-btn.active {
  background: #007bff;
  color: white;
}

/* Calendar Grid - Month View */
.calendar-grid {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
}

.calendar-header-day {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #495057;
  border-right: 1px solid #e9ecef;
}

.calendar-header-day:last-child {
  border-right: none;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  min-height: 120px;
  padding: 0.5rem;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
}

.calendar-day:hover {
  background: #f8f9fa;
}

.calendar-day:nth-child(7n) {
  border-right: none;
}

.calendar-day.other-month {
  background: #f8f9fa;
  color: #adb5bd;
}

.calendar-day.today {
  background: #e3f2fd;
}

.calendar-day.selected {
  background: #007bff;
  color: white;
}

.day-number {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.day-appointments {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.appointment-dot {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  cursor: pointer;
}

.more-appointments {
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
  margin-top: 0.25rem;
}

/* Week View */
.week-view {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.week-day {
  background: white;
  min-height: 400px;
}

.week-day-header {
  background: #f8f9fa;
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.week-day-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.week-day-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.week-day-appointments {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.week-appointment {
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.week-appointment:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.appointment-time {
  font-size: 0.75rem;
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.25rem;
}

.appointment-customer {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.125rem;
}

.appointment-service {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Day View */
.day-view {
  max-height: 600px;
  overflow-y: auto;
}

.day-hour {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  min-height: 60px;
}

.hour-label {
  width: 80px;
  padding: 1rem;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  font-weight: 500;
  color: #6c757d;
  text-align: center;
  font-size: 0.875rem;
}

.hour-appointments {
  flex: 1;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.day-appointment {
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
}

.day-appointment:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.appointment-staff {
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Calendar Legend */
.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #495057;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .calendar-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .calendar-navigation {
    justify-content: center;
  }
  
  .calendar-actions {
    justify-content: center;
  }
  
  .week-view {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .calendar-legend {
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .appointment-calendar {
    padding: 1rem;
  }
  
  .calendar-title {
    font-size: 1.25rem;
    min-width: 150px;
  }
  
  .calendar-day {
    min-height: 80px;
    padding: 0.25rem;
  }
  
  .day-number {
    font-size: 0.875rem;
  }
  
  .week-view {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .week-day {
    min-height: 300px;
  }
  
  .hour-label {
    width: 60px;
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  .day-hour {
    min-height: 50px;
  }
}

@media (max-width: 480px) {
  .calendar-controls {
    gap: 0.5rem;
  }
  
  .calendar-title {
    font-size: 1rem;
    min-width: 120px;
  }
  
  .view-mode-selector {
    width: 100%;
  }
  
  .view-btn {
    flex: 1;
    padding: 0.75rem;
  }
  
  .calendar-day {
    min-height: 60px;
    padding: 0.125rem;
  }
  
  .day-number {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }
  
  .appointment-dot {
    height: 3px;
  }
  
  .week-view {
    grid-template-columns: 1fr;
  }
  
  .calendar-legend {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .legend-item {
    justify-content: center;
  }
}
