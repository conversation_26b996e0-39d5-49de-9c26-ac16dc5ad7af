import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const DashboardCharts = ({ chartData }) => {
  if (!chartData) {
    return (
      <div className="charts-loading">
        <p>Loading charts...</p>
      </div>
    );
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#3498db',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#3498db',
        borderWidth: 1,
        cornerRadius: 8
      }
    },
    cutout: '60%'
  };

  return (
    <div className="dashboard-charts">
      <h2 className="section-title">Analytics Overview</h2>
      
      <div className="charts-grid">
        {/* Revenue Trend Chart */}
        <div className="chart-card">
          <div className="chart-header">
            <h3 className="chart-title">Daily Revenue Trend</h3>
            <p className="chart-subtitle">Last 7 days</p>
          </div>
          <div className="chart-container">
            <Line data={chartData.revenueChart} options={chartOptions} />
          </div>
        </div>

        {/* Appointments Chart */}
        <div className="chart-card">
          <div className="chart-header">
            <h3 className="chart-title">Daily Appointments</h3>
            <p className="chart-subtitle">Last 7 days</p>
          </div>
          <div className="chart-container">
            <Line data={chartData.appointmentsChart} options={chartOptions} />
          </div>
        </div>

        {/* Monthly Revenue Bar Chart */}
        <div className="chart-card chart-card-wide">
          <div className="chart-header">
            <h3 className="chart-title">Monthly Revenue</h3>
            <p className="chart-subtitle">Last 6 months</p>
          </div>
          <div className="chart-container">
            <Bar data={chartData.monthlyRevenueChart} options={chartOptions} />
          </div>
        </div>

        {/* Service Popularity Doughnut Chart */}
        <div className="chart-card">
          <div className="chart-header">
            <h3 className="chart-title">Service Popularity</h3>
            <p className="chart-subtitle">This month</p>
          </div>
          <div className="chart-container">
            <Doughnut data={chartData.servicesChart} options={doughnutOptions} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCharts;
