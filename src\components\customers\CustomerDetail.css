/* Customer Detail Styles */
.customer-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.customer-detail-container {
  background: white;
  border-radius: 16px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

/* Header */
.customer-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.customer-header-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.customer-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.customer-title h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.customer-title .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.customer-title .status-badge.active {
  background: rgba(34, 197, 94, 0.2);
  color: #dcfce7;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.customer-title .status-badge.inactive {
  background: rgba(239, 68, 68, 0.2);
  color: #fee2e2;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.customer-header-actions {
  display: flex;
  gap: 0.75rem;
}

.customer-header-actions .btn {
  padding: 0.5rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.customer-header-actions .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.close-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

/* Quick Stats */
.customer-quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 10px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Tabs */
.customer-detail-tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #f8fafc;
  color: #374151;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #f8fafc;
}

/* Content */
.customer-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* Profile Tab */
.profile-tab {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section h3 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.profile-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.profile-item label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.profile-item span {
  color: #1e293b;
  font-weight: 600;
}

.address-info {
  color: #374151;
  line-height: 1.6;
}

.no-data {
  color: #9ca3af;
  font-style: italic;
}

/* History Tab */
.history-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.history-count {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.no-history {
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
}

.no-history-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-date {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.history-details {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.5rem;
  align-items: start;
}

.history-service {
  font-size: 1rem;
  color: #1e293b;
}

.history-staff {
  color: #64748b;
  font-size: 0.875rem;
}

.history-amount {
  color: #059669;
  font-weight: 700;
  font-size: 1.125rem;
  grid-row: 1 / 3;
  text-align: right;
}

.history-notes {
  grid-column: 1 / -1;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
  color: #64748b;
  font-size: 0.875rem;
}

/* Preferences Tab */
.preferences-tab {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.preferences-section h3 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.preferences-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.preference-tag {
  padding: 0.5rem 1rem;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.preferences-text {
  color: #374151;
  line-height: 1.6;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .customer-detail-overlay {
    padding: 0;
  }

  .customer-detail-container {
    max-height: 100vh;
    border-radius: 0;
  }

  .customer-detail-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .customer-header-info {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .customer-header-actions {
    justify-content: center;
  }

  .customer-quick-stats {
    grid-template-columns: 1fr;
    padding: 1.5rem;
  }

  .customer-detail-content {
    padding: 1.5rem;
  }

  .profile-grid {
    grid-template-columns: 1fr;
  }

  .history-details {
    grid-template-columns: 1fr;
  }

  .history-amount {
    text-align: left;
    grid-row: auto;
  }
}
