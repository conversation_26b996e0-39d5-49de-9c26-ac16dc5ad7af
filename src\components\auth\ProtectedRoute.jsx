import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

// Loading spinner component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="flex flex-col items-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p className="mt-4 text-gray-600">Loading...</p>
    </div>
  </div>
);

// Unauthorized access component
const UnauthorizedAccess = ({ requiredRole, userRole }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
      <div className="mb-4">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
      <p className="text-sm text-gray-500 mb-6">
        You don't have permission to access this page. 
        {requiredRole && (
          <span className="block mt-1">
            Required role: <span className="font-medium">{requiredRole}</span>
          </span>
        )}
        {userRole && (
          <span className="block mt-1">
            Your role: <span className="font-medium">{userRole}</span>
          </span>
        )}
      </p>
      <button
        onClick={() => window.history.back()}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
      >
        Go Back
      </button>
    </div>
  </div>
);

// Protected Route component
const ProtectedRoute = ({ 
  children, 
  requiredRole = null, 
  requiredRoles = null, 
  requiredPermission = null,
  fallbackPath = '/signin',
  showUnauthorized = true 
}) => {
  const { isAuthenticated, isLoading, user, hasRole, hasAnyRole, hasPermission } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRole && !hasRole(requiredRole)) {
    if (showUnauthorized) {
      return <UnauthorizedAccess requiredRole={requiredRole} userRole={user?.role} />;
    }
    return <Navigate to="/dashboard" replace />;
  }

  // Check multiple roles access
  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    if (showUnauthorized) {
      return <UnauthorizedAccess requiredRole={requiredRoles.join(' or ')} userRole={user?.role} />;
    }
    return <Navigate to="/dashboard" replace />;
  }

  // Check permission-based access
  if (requiredPermission && !hasPermission(requiredPermission)) {
    if (showUnauthorized) {
      return <UnauthorizedAccess requiredRole={`Permission: ${requiredPermission}`} userRole={user?.role} />;
    }
    return <Navigate to="/dashboard" replace />;
  }

  // User is authenticated and authorized
  return children;
};

// Higher-order component for role-based protection
export const withRoleProtection = (Component, requiredRole) => {
  return (props) => (
    <ProtectedRoute requiredRole={requiredRole}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Higher-order component for multiple roles protection
export const withRolesProtection = (Component, requiredRoles) => {
  return (props) => (
    <ProtectedRoute requiredRoles={requiredRoles}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Higher-order component for permission-based protection
export const withPermissionProtection = (Component, requiredPermission) => {
  return (props) => (
    <ProtectedRoute requiredPermission={requiredPermission}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Specific role-based route components
export const AdminRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRole="admin" {...props}>
    {children}
  </ProtectedRoute>
);

export const StaffRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRoles={['admin', 'staff']} {...props}>
    {children}
  </ProtectedRoute>
);

export const ReceptionistRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRoles={['admin', 'receptionist']} {...props}>
    {children}
  </ProtectedRoute>
);

export const StaffOrReceptionistRoute = ({ children, ...props }) => (
  <ProtectedRoute requiredRoles={['admin', 'staff', 'receptionist']} {...props}>
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;
