import React, { useState, useRef, useEffect } from 'react';
import './FloatingActionButton.css';

// Main Floating Action Button
export const FloatingActionButton = ({
  icon = '+',
  onClick,
  className = '',
  size = 'md',
  color = 'primary',
  position = 'bottom-right',
  tooltip,
  disabled = false,
  loading = false,
  children,
  ...props
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const fabRef = useRef(null);

  const handleClick = (e) => {
    if (disabled || loading) return;
    
    if (children) {
      setIsExpanded(!isExpanded);
    } else if (onClick) {
      onClick(e);
    }
  };

  const handleMouseEnter = () => {
    if (tooltip && !isExpanded) {
      setShowTooltip(true);
    }
  };

  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (fabRef.current && !fabRef.current.contains(event.target)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  return (
    <div
      ref={fabRef}
      className={`fab-container ${position} ${className}`}
      {...props}
    >
      {/* Sub-actions (when expanded) */}
      {children && isExpanded && (
        <div className="fab-menu">
          {React.Children.map(children, (child, index) => (
            <div
              key={index}
              className="fab-menu-item"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {child}
            </div>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <button
        className={`fab ${size} ${color} ${disabled ? 'disabled' : ''} ${loading ? 'loading' : ''} ${isExpanded ? 'expanded' : ''}`}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        disabled={disabled}
        aria-label={tooltip}
      >
        {loading ? (
          <div className="fab-spinner" />
        ) : (
          <span className={`fab-icon ${isExpanded ? 'rotated' : ''}`}>
            {icon}
          </span>
        )}
      </button>

      {/* Tooltip */}
      {tooltip && showTooltip && !isExpanded && (
        <div className="fab-tooltip">
          {tooltip}
        </div>
      )}
    </div>
  );
};

// Sub Floating Action Button (for menu items)
export const SubFAB = ({
  icon,
  label,
  onClick,
  className = '',
  color = 'secondary',
  disabled = false,
  ...props
}) => {
  const [showLabel, setShowLabel] = useState(false);

  return (
    <div className="sub-fab-container">
      {label && showLabel && (
        <div className="sub-fab-label">
          {label}
        </div>
      )}
      <button
        className={`sub-fab ${color} ${disabled ? 'disabled' : ''} ${className}`}
        onClick={onClick}
        onMouseEnter={() => setShowLabel(true)}
        onMouseLeave={() => setShowLabel(false)}
        disabled={disabled}
        aria-label={label}
        {...props}
      >
        <span className="sub-fab-icon">{icon}</span>
      </button>
    </div>
  );
};

// Speed Dial Component (Multiple FABs)
export const SpeedDial = ({
  actions = [],
  icon = '+',
  className = '',
  position = 'bottom-right',
  direction = 'up',
  tooltip = 'Quick Actions',
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const speedDialRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (speedDialRef.current && !speedDialRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleActionClick = (action) => {
    if (action.onClick) {
      action.onClick();
    }
    setIsOpen(false);
  };

  return (
    <div
      ref={speedDialRef}
      className={`speed-dial ${position} ${direction} ${isOpen ? 'open' : ''} ${className}`}
      {...props}
    >
      {/* Backdrop */}
      {isOpen && <div className="speed-dial-backdrop" />}

      {/* Actions */}
      {isOpen && (
        <div className="speed-dial-actions">
          {actions.map((action, index) => (
            <div
              key={index}
              className="speed-dial-action"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              {action.label && (
                <div className="speed-dial-label">
                  {action.label}
                </div>
              )}
              <button
                className={`speed-dial-button ${action.color || 'secondary'}`}
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
                aria-label={action.label}
              >
                <span className="speed-dial-icon">{action.icon}</span>
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Main Button */}
      <FloatingActionButton
        icon={icon}
        onClick={handleToggle}
        tooltip={tooltip}
        className={isOpen ? 'expanded' : ''}
      />
    </div>
  );
};

// Quick Action FAB (with predefined actions)
export const QuickActionFAB = ({
  onNewCustomer,
  onNewAppointment,
  onNewService,
  onQuickSearch,
  position = 'bottom-right',
  ...props
}) => {
  const actions = [
    onNewCustomer && {
      icon: '👤',
      label: 'New Customer',
      onClick: onNewCustomer,
      color: 'primary'
    },
    onNewAppointment && {
      icon: '📅',
      label: 'New Appointment',
      onClick: onNewAppointment,
      color: 'success'
    },
    onNewService && {
      icon: '✂️',
      label: 'New Service',
      onClick: onNewService,
      color: 'warning'
    },
    onQuickSearch && {
      icon: '🔍',
      label: 'Quick Search',
      onClick: onQuickSearch,
      color: 'info'
    }
  ].filter(Boolean);

  return (
    <SpeedDial
      actions={actions}
      icon="+"
      tooltip="Quick Actions"
      position={position}
      {...props}
    />
  );
};

// Notification FAB
export const NotificationFAB = ({
  count = 0,
  onClick,
  position = 'top-right',
  maxCount = 99,
  ...props
}) => {
  const displayCount = count > maxCount ? `${maxCount}+` : count;

  return (
    <div className={`notification-fab-container ${position}`}>
      <FloatingActionButton
        icon="🔔"
        onClick={onClick}
        tooltip={`${count} notifications`}
        className="notification-fab"
        {...props}
      />
      {count > 0 && (
        <div className="notification-badge">
          {displayCount}
        </div>
      )}
    </div>
  );
};

// Chat FAB
export const ChatFAB = ({
  isOnline = false,
  unreadCount = 0,
  onClick,
  position = 'bottom-left',
  ...props
}) => {
  return (
    <div className={`chat-fab-container ${position}`}>
      <FloatingActionButton
        icon="💬"
        onClick={onClick}
        tooltip="Chat Support"
        className={`chat-fab ${isOnline ? 'online' : 'offline'}`}
        {...props}
      />
      {unreadCount > 0 && (
        <div className="chat-badge">
          {unreadCount}
        </div>
      )}
      <div className={`status-indicator ${isOnline ? 'online' : 'offline'}`} />
    </div>
  );
};

export default FloatingActionButton;
