/* Appointment Detail Styles */
.appointment-detail {
  max-width: 700px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Header */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.header-content h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.75rem;
  font-weight: 600;
}

.appointment-id {
  color: #6c757d;
  font-size: 0.9rem;
  font-family: monospace;
}

.close-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #6c757d;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* Detail Content */
.detail-content {
  margin-bottom: 2rem;
}

.detail-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f1f3f4;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-section h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-section h3::before {
  content: '';
  width: 3px;
  height: 16px;
  background: #007bff;
  border-radius: 2px;
}

/* Date & Time Info */
.datetime-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.date-display {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.date-icon {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.date {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.time-range {
  color: #007bff;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.duration {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Info Rows */
.customer-info,
.service-info,
.history-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.label {
  font-weight: 500;
  color: #495057;
  min-width: 100px;
}

.value {
  color: #2c3e50;
  text-align: right;
}

.value a {
  color: #007bff;
  text-decoration: none;
}

.value a:hover {
  text-decoration: underline;
}

.value.price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #28a745;
}

/* Status Section */
.status-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.current-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-actions label {
  font-weight: 500;
  color: #495057;
}

.status-select {
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.status-select:focus {
  outline: none;
  border-color: #007bff;
}

/* Notes Section */
.notes-section {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.notes-display {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notes-text {
  margin: 0;
  color: #495057;
  line-height: 1.6;
  font-style: italic;
}

.notes-edit {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notes-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.notes-textarea:focus {
  outline: none;
  border-color: #007bff;
}

.notes-actions {
  display: flex;
  gap: 0.5rem;
}

/* Detail Actions */
.detail-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 2px solid #e9ecef;
  flex-wrap: wrap;
  gap: 1rem;
}

.action-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.danger-actions {
  display: flex;
  gap: 1rem;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .appointment-detail {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .header-content h2 {
    font-size: 1.5rem;
  }
  
  .date-display {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .value {
    text-align: left;
  }
  
  .status-actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-group,
  .danger-actions {
    justify-content: center;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .appointment-detail {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .header-content h2 {
    font-size: 1.25rem;
  }
  
  .detail-section h3 {
    font-size: 1rem;
  }
  
  .datetime-info {
    padding: 1rem;
  }
  
  .date {
    font-size: 1rem;
  }
  
  .notes-actions {
    flex-direction: column;
  }
  
  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
}
