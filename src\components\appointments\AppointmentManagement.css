/* Appointment Management Styles */
.appointment-management {
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Styles */
.appointment-management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 1.1rem;
}

.header-stats {
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.stat-card {
  text-align: center;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  min-width: 100px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.95;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Enhanced New Appointment Button */
.new-appointment-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.new-appointment-btn:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.new-appointment-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.new-appointment-btn .btn-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
  display: inline-block;
  transition: transform 0.3s ease;
}

.new-appointment-btn:hover .btn-icon {
  transform: scale(1.1);
}

.new-appointment-btn .btn-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Ripple effect */
.new-appointment-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
}

.new-appointment-btn:active::before {
  width: 300px;
  height: 300px;
  top: 50%;
  left: 50%;
}

/* Enhanced Navigation Styles */
.appointment-navigation {
  margin-bottom: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.appointment-navigation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #4CAF50 100%);
  border-radius: 16px 16px 0 0;
}

.nav-container {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.75rem;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.nav-btn.active:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.nav-icon {
  font-size: 1.2rem;
  display: inline-block;
  transition: transform 0.3s ease;
}

.nav-btn:hover .nav-icon {
  transform: scale(1.1);
}

.nav-btn.active .nav-icon {
  transform: scale(1.05);
}

.nav-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%);
  opacity: 0.6;
}

/* Content Area */
.appointment-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .appointment-management-header {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
    gap: 1.5rem;
  }

  .header-actions {
    justify-content: center;
  }

  .new-appointment-btn {
    padding: 0.875rem 1.75rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .appointment-management {
    padding: 1rem;
  }

  .appointment-management-header {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .header-stats {
    flex-wrap: wrap;
    gap: 1.25rem;
    justify-content: center;
  }

  .stat-card {
    min-width: 85px;
    padding: 1rem 1.25rem;
  }

  .stat-number {
    font-size: 1.75rem;
  }
  
  .appointment-navigation {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .nav-container {
    gap: 1rem;
    justify-content: center;
  }

  .nav-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
    min-width: 120px;
    gap: 0.5rem;
  }

  .nav-icon {
    font-size: 1.1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }

  .new-appointment-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .new-appointment-btn .btn-icon {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .appointment-management-header {
    text-align: center;
    padding: 1rem;
    gap: 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-stats {
    justify-content: center;
    gap: 1rem;
  }

  .stat-card {
    min-width: 75px;
    padding: 0.75rem 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }
  
  .appointment-navigation {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .nav-container {
    gap: 0.75rem;
    flex-direction: column;
  }

  .nav-btn {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    min-width: 100%;
    max-width: 200px;
    gap: 0.5rem;
  }

  .nav-icon {
    font-size: 1rem;
  }

  .nav-text {
    font-size: 0.875rem;
  }

  .new-appointment-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    width: 100%;
    max-width: 250px;
  }

  .new-appointment-btn .btn-icon {
    font-size: 1rem;
  }
}

/* View Not Found */
.view-not-found {
  padding: 4rem;
  text-align: center;
  color: #6c757d;
}

/* Action Buttons */
.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 1rem;
}

.action-btn:hover {
  background: #f8f9fa;
}

.edit-btn:hover {
  background: #e3f2fd;
}

.delete-btn:hover {
  background: #ffebee;
}
