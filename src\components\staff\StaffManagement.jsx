import React, { useState, useEffect } from 'react';
import StaffProfile from './StaffProfile';
import StaffSchedule from './StaffSchedule';
import StaffPerformance from './StaffPerformance';
import StaffForm from './StaffForm';
import staffService from '../../services/staffService';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';
import './StaffManagement.css';

const StaffManagement = () => {
  const [currentView, setCurrentView] = useState('list'); // 'list', 'profile', 'schedule', 'performance', 'form'
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [staff, setStaff] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const { user, isAdmin } = useAuth();

  useEffect(() => {
    loadData();
  }, [refreshTrigger]);

  const loadData = async () => {
    try {
      setLoading(true);
      const staffData = staffService.getAllStaff();
      const statsData = staffService.getStaffStats();
      
      setStaff(staffData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading staff data:', error);
      toast.error('Failed to load staff data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleStaffSelect = (staffMember, view = 'profile') => {
    setSelectedStaff(staffMember);
    setCurrentView(view);
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedStaff(null);
  };

  const handleStaffUpdate = async (staffId, updates) => {
    try {
      await staffService.updateStaff(staffId, updates);
      toast.success('Staff member updated successfully');
      handleRefresh();
    } catch (error) {
      console.error('Error updating staff:', error);
      toast.error('Failed to update staff member');
    }
  };

  const handleStaffDelete = async (staffId) => {
    if (!window.confirm('Are you sure you want to delete this staff member?')) {
      return;
    }

    try {
      await staffService.deleteStaff(staffId);
      toast.success('Staff member deleted successfully');
      handleRefresh();
      if (selectedStaff && selectedStaff.id === staffId) {
        handleBackToList();
      }
    } catch (error) {
      console.error('Error deleting staff:', error);
      toast.error('Failed to delete staff member');
    }
  };

  const handleAddStaff = () => {
    setSelectedStaff(null);
    setCurrentView('form');
  };

  const handleStaffEdit = (staff) => {
    setSelectedStaff(staff);
    setCurrentView('form');
  };

  const handleStaffSave = () => {
    handleRefresh();
    setCurrentView('list');
    setSelectedStaff(null);
  };

  const handleFormCancel = () => {
    setCurrentView('list');
    setSelectedStaff(null);
  };

  // Filter staff based on search and filters
  const filteredStaff = staff.filter(staffMember => {
    const matchesSearch = searchTerm === '' ||
      staffMember.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staffMember.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staffMember.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterRole === 'all' || staffMember.role === filterRole;
    const matchesStatus = filterStatus === 'all' || staffMember.status === filterStatus;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: {
        label: 'Active',
        className: 'status-active',
        icon: '✅',
        color: '#10b981'
      },
      inactive: {
        label: 'Inactive',
        className: 'status-inactive',
        icon: '⏸️',
        color: '#6b7280'
      },
      on_leave: {
        label: 'On Leave',
        className: 'status-leave',
        icon: '🏖️',
        color: '#f59e0b'
      },
      terminated: {
        label: 'Terminated',
        className: 'status-terminated',
        icon: '❌',
        color: '#ef4444'
      }
    };

    const config = statusConfig[status] || statusConfig.active;
    return (
      <span className={`status-badge ${config.className}`}>
        <span className="status-icon">{config.icon}</span>
        <span className="status-label">{config.label}</span>
        <span className="status-indicator" style={{ backgroundColor: config.color }}></span>
      </span>
    );
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      stylist: { label: 'Stylist', icon: '💇‍♀️' },
      beautician: { label: 'Beautician', icon: '✨' },
      nail_technician: { label: 'Nail Tech', icon: '💅' },
      massage_therapist: { label: 'Massage', icon: '🧘‍♀️' },
      receptionist: { label: 'Reception', icon: '📞' },
      manager: { label: 'Manager', icon: '👔' }
    };

    const config = roleConfig[role] || { label: role, icon: '👤' };
    return (
      <span className="role-badge">
        <span className="role-icon">{config.icon}</span>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="staff-management">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading staff...</p>
        </div>
      </div>
    );
  }

  // Render different views
  if (currentView === 'profile' && selectedStaff) {
    return (
      <StaffProfile
        staff={selectedStaff}
        onBack={handleBackToList}
        onUpdate={handleStaffUpdate}
        onDelete={handleStaffDelete}
        canEdit={isAdmin}
      />
    );
  }

  if (currentView === 'schedule' && selectedStaff) {
    return (
      <StaffSchedule
        staff={selectedStaff}
        onBack={handleBackToList}
        canEdit={isAdmin}
      />
    );
  }

  if (currentView === 'performance' && selectedStaff) {
    return (
      <StaffPerformance
        staff={selectedStaff}
        onBack={handleBackToList}
        canEdit={isAdmin}
      />
    );
  }

  if (currentView === 'form') {
    return (
      <StaffForm
        staff={selectedStaff}
        onSave={handleStaffSave}
        onCancel={handleFormCancel}
      />
    );
  }

  return (
    <div className="staff-management">
      {/* Header */}
      <div className="staff-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="page-title">
              <span className="title-icon">👥</span>
              Staff Management
            </h1>
            <p className="page-subtitle">
              Manage your salon staff, schedules, and performance
            </p>
          </div>
          
          <div className="header-actions">
            <button
              className="btn btn-primary"
              onClick={handleAddStaff}
            >
              <span className="btn-icon">➕</span>
              Add Staff
            </button>
            <button
              className="btn btn-secondary"
              onClick={handleRefresh}
            >
              <span className="btn-icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalStaff}</div>
            <div className="stat-label">Total Staff</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <div className="stat-value">{stats.activeStaff}</div>
            <div className="stat-label">Active Staff</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">🏖️</div>
          <div className="stat-content">
            <div className="stat-value">{stats.onLeave}</div>
            <div className="stat-label">On Leave</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <div className="stat-value">${stats.avgHourlyRate}</div>
            <div className="stat-label">Avg. Hourly Rate</div>
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="staff-controls">
        <div className="search-section">
          <div className="search-box">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="Search staff..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="filter-section">
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Roles</option>
            <option value="stylist">Stylist</option>
            <option value="beautician">Beautician</option>
            <option value="nail_technician">Nail Technician</option>
            <option value="massage_therapist">Massage Therapist</option>
            <option value="receptionist">Receptionist</option>
            <option value="manager">Manager</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="on_leave">On Leave</option>
            <option value="terminated">Terminated</option>
          </select>
        </div>
      </div>

      {/* Results Count */}
      <div className="results-info">
        <span className="results-count">
          {filteredStaff.length} staff member{filteredStaff.length !== 1 ? 's' : ''} found
        </span>
      </div>

      {/* Staff List */}
      {filteredStaff.length === 0 ? (
        <div className="no-staff">
          <div className="no-staff-icon">👥</div>
          <h3>No staff members found</h3>
          <p>Try adjusting your search or filter criteria</p>
        </div>
      ) : (
        <div className="staff-grid">
          {filteredStaff.map((staffMember) => (
            <div key={staffMember.id} className="staff-card">
              <div className="staff-header">
                <div className="staff-avatar">
                  {staffMember.avatar ? (
                    <img src={staffMember.avatar} alt={`${staffMember.firstName} ${staffMember.lastName}`} />
                  ) : (
                    <div className="avatar-placeholder">
                      {staffMember.firstName[0]}{staffMember.lastName[0]}
                    </div>
                  )}
                </div>
                <div className="staff-info">
                  <h3 className="staff-name">
                    {staffMember.firstName} {staffMember.lastName}
                  </h3>
                </div>
                {getStatusBadge(staffMember.status)}
              </div>

              <div className="staff-details">
                <div className="detail-row">
                  <span className="detail-label">Role:</span>
                  {getRoleBadge(staffMember.role)}
                </div>
                
                <div className="detail-row">
                  <span className="detail-label">Email:</span>
                  <span className="detail-value">{staffMember.email}</span>
                </div>
                
                <div className="detail-row">
                  <span className="detail-label">Phone:</span>
                  <span className="detail-value">{staffMember.phone}</span>
                </div>
                
                <div className="detail-row">
                  <span className="detail-label">Hire Date:</span>
                  <span className="detail-value">
                    {new Date(staffMember.hireDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Hourly Rate:</span>
                  <span className="detail-value">${staffMember.hourlyRate}/hr</span>
                </div>

                {staffMember.specialties && staffMember.specialties.length > 0 && (
                  <div className="specialties">
                    <span className="detail-label">Specialties:</span>
                    <div className="specialty-tags">
                      {staffMember.specialties.slice(0, 3).map((specialty, index) => (
                        <span key={index} className="specialty-tag">
                          {specialty}
                        </span>
                      ))}
                      {staffMember.specialties.length > 3 && (
                        <span className="specialty-tag more">
                          +{staffMember.specialties.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="staff-actions">
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => handleStaffEdit(staffMember)}
                >
                  <span className="btn-icon">✏️</span>
                  Edit
                </button>
                <button
                  className="btn btn-secondary btn-sm"
                  onClick={() => handleStaffSelect(staffMember, 'profile')}
                >
                  <span className="btn-icon">👤</span>
                  Profile
                </button>
                <button
                  className="btn btn-secondary btn-sm"
                  onClick={() => handleStaffSelect(staffMember, 'schedule')}
                >
                  <span className="btn-icon">📅</span>
                  Schedule
                </button>
                <button
                  className="btn btn-secondary btn-sm"
                  onClick={() => handleStaffSelect(staffMember, 'performance')}
                >
                  <span className="btn-icon">📊</span>
                  Performance
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default StaffManagement;
