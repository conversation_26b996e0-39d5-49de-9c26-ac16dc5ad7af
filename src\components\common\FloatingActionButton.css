/* Floating Action Button Styles */

/* FAB Container */
.fab-container {
  position: fixed;
  z-index: var(--z-fixed);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Positioning */
.fab-container.bottom-right {
  bottom: var(--space-6);
  right: var(--space-6);
}

.fab-container.bottom-left {
  bottom: var(--space-6);
  left: var(--space-6);
}

.fab-container.top-right {
  top: var(--space-6);
  right: var(--space-6);
}

.fab-container.top-left {
  top: var(--space-6);
  left: var(--space-6);
}

.fab-container.center {
  bottom: var(--space-6);
  left: 50%;
  transform: translateX(-50%);
}

/* Main FAB Button */
.fab {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-weight: var(--font-weight-bold);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  user-select: none;
}

.fab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  transition: transform var(--transition-fast);
}

.fab:hover::before {
  transform: scale(1);
}

.fab:active {
  transform: scale(0.95);
}

.fab:focus {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* FAB Sizes */
.fab.sm {
  width: 40px;
  height: 40px;
  font-size: var(--font-size-sm);
}

.fab.md {
  width: 56px;
  height: 56px;
  font-size: var(--font-size-lg);
}

.fab.lg {
  width: 72px;
  height: 72px;
  font-size: var(--font-size-xl);
}

/* FAB Colors */
.fab.primary {
  background: var(--gradient-primary);
  color: white;
}

.fab.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.fab.secondary {
  background: var(--gradient-secondary);
  color: white;
}

.fab.secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.fab.success {
  background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
  color: white;
}

.fab.warning {
  background: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
  color: white;
}

.fab.error,
.fab.danger {
  background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
  color: white;
}

.fab.info {
  background: linear-gradient(135deg, var(--color-info-500) 0%, var(--color-info-600) 100%);
  color: white;
}

/* FAB States */
.fab.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-md);
}

.fab.loading {
  cursor: wait;
}

.fab.expanded .fab-icon {
  transform: rotate(45deg);
}

/* FAB Icon */
.fab-icon {
  transition: transform var(--transition-base);
  position: relative;
  z-index: 1;
}

.fab-icon.rotated {
  transform: rotate(45deg);
}

/* FAB Spinner */
.fab-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
  z-index: 1;
}

/* FAB Menu */
.fab-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.fab-menu-item {
  animation: fabMenuSlideIn 0.3s ease-out;
}

/* FAB Tooltip */
.fab-tooltip {
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--color-gray-800);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  margin-right: var(--space-3);
  box-shadow: var(--shadow-md);
  animation: tooltipFadeIn 0.2s ease-out;
}

.fab-tooltip::after {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-left-color: var(--color-gray-800);
}

/* Sub FAB */
.sub-fab-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.sub-fab {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.sub-fab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  transition: transform var(--transition-fast);
}

.sub-fab:hover::before {
  transform: scale(1);
}

.sub-fab:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.sub-fab.primary {
  background: var(--gradient-primary);
  color: white;
}

.sub-fab.secondary {
  background: var(--color-gray-600);
  color: white;
}

.sub-fab.success {
  background: var(--color-success-500);
  color: white;
}

.sub-fab.warning {
  background: var(--color-warning-500);
  color: white;
}

.sub-fab.error,
.sub-fab.danger {
  background: var(--color-error-500);
  color: white;
}

.sub-fab.info {
  background: var(--color-info-500);
  color: white;
}

.sub-fab.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.sub-fab-label {
  background: var(--color-gray-800);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
  animation: tooltipFadeIn 0.2s ease-out;
}

/* Speed Dial */
.speed-dial {
  position: fixed;
  z-index: var(--z-fixed);
}

.speed-dial.bottom-right {
  bottom: var(--space-6);
  right: var(--space-6);
}

.speed-dial.bottom-left {
  bottom: var(--space-6);
  left: var(--space-6);
}

.speed-dial.top-right {
  top: var(--space-6);
  right: var(--space-6);
}

.speed-dial.top-left {
  top: var(--space-6);
  left: var(--space-6);
}

.speed-dial-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: -1;
  animation: backdropFadeIn 0.2s ease-out;
}

.speed-dial-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.speed-dial.up .speed-dial-actions {
  flex-direction: column-reverse;
  margin-bottom: var(--space-3);
  margin-top: 0;
}

.speed-dial.down .speed-dial-actions {
  flex-direction: column;
  margin-top: var(--space-3);
  margin-bottom: 0;
}

.speed-dial.left .speed-dial-actions {
  flex-direction: row-reverse;
  margin-right: var(--space-3);
  margin-bottom: 0;
}

.speed-dial.right .speed-dial-actions {
  flex-direction: row;
  margin-left: var(--space-3);
  margin-bottom: 0;
}

.speed-dial-action {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  animation: speedDialSlideIn 0.3s ease-out;
}

.speed-dial-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.speed-dial-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0);
  transition: transform var(--transition-fast);
}

.speed-dial-button:hover::before {
  transform: scale(1);
}

.speed-dial-button:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.speed-dial-button.primary {
  background: var(--gradient-primary);
  color: white;
}

.speed-dial-button.secondary {
  background: var(--color-gray-600);
  color: white;
}

.speed-dial-button.success {
  background: var(--color-success-500);
  color: white;
}

.speed-dial-button.warning {
  background: var(--color-warning-500);
  color: white;
}

.speed-dial-button.error,
.speed-dial-button.danger {
  background: var(--color-error-500);
  color: white;
}

.speed-dial-button.info {
  background: var(--color-info-500);
  color: white;
}

.speed-dial-label {
  background: var(--color-gray-800);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
}

/* Notification FAB */
.notification-fab-container {
  position: fixed;
  z-index: var(--z-fixed);
}

.notification-fab-container.top-right {
  top: var(--space-6);
  right: var(--space-6);
}

.notification-fab-container.top-left {
  top: var(--space-6);
  left: var(--space-6);
}

.notification-fab {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--color-error-500);
  color: white;
  border-radius: var(--radius-full);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  animation: badgeBounce 0.5s ease-out;
}

/* Chat FAB */
.chat-fab-container {
  position: fixed;
  z-index: var(--z-fixed);
}

.chat-fab-container.bottom-left {
  bottom: var(--space-6);
  left: var(--space-6);
}

.chat-fab-container.bottom-right {
  bottom: var(--space-6);
  right: var(--space-6);
}

.chat-fab {
  position: relative;
}

.chat-fab.online {
  background: var(--color-success-500);
}

.chat-fab.offline {
  background: var(--color-gray-500);
}

.chat-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--color-error-500);
  color: white;
  border-radius: var(--radius-full);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

.status-indicator.online {
  background: var(--color-success-500);
  animation: statusPulse 2s infinite;
}

.status-indicator.offline {
  background: var(--color-gray-400);
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fabMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes speedDialSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes tooltipFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes backdropFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes badgeBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes statusPulse {
  0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
  100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

/* Dark Mode Support */
[data-theme="dark"] .fab-tooltip,
[data-theme="dark"] .sub-fab-label,
[data-theme="dark"] .speed-dial-label {
  background: var(--color-gray-700);
  color: var(--color-gray-100);
}

[data-theme="dark"] .fab-tooltip::after {
  border-left-color: var(--color-gray-700);
}

/* Responsive Design */
@media (max-width: 768px) {
  .fab-container.bottom-right,
  .speed-dial.bottom-right,
  .notification-fab-container.top-right,
  .chat-fab-container.bottom-right {
    right: var(--space-4);
  }
  
  .fab-container.bottom-left,
  .speed-dial.bottom-left,
  .notification-fab-container.top-left,
  .chat-fab-container.bottom-left {
    left: var(--space-4);
  }
  
  .fab-container.bottom-right,
  .fab-container.bottom-left,
  .speed-dial.bottom-right,
  .speed-dial.bottom-left,
  .chat-fab-container.bottom-right,
  .chat-fab-container.bottom-left {
    bottom: var(--space-4);
  }
  
  .fab-container.top-right,
  .fab-container.top-left,
  .speed-dial.top-right,
  .speed-dial.top-left,
  .notification-fab-container.top-right,
  .notification-fab-container.top-left {
    top: var(--space-4);
  }
  
  .fab.lg {
    width: 56px;
    height: 56px;
    font-size: var(--font-size-lg);
  }
  
  .speed-dial-button {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-sm);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .fab,
  .sub-fab,
  .speed-dial-button {
    transition: none;
  }
  
  .fab::before,
  .sub-fab::before,
  .speed-dial-button::before {
    transition: none;
  }
  
  .fab-menu-item,
  .speed-dial-action {
    animation: none;
  }
  
  .notification-badge,
  .status-indicator.online {
    animation: none;
  }
}
