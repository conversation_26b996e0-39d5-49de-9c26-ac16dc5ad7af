import React, { useState, useEffect } from 'react';
import appointmentService, { TIME_SLOTS } from '../../services/appointmentService';
import './TimeSlotPicker.css';

const TimeSlotPicker = ({ 
  selectedDate, 
  selectedStaff, 
  selectedService, 
  onTimeSelect, 
  selectedTime,
  excludeAppointmentId = null 
}) => {
  const [availableSlots, setAvailableSlots] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedDate && selectedStaff) {
      loadAvailableSlots();
    }
  }, [selectedDate, selectedStaff, selectedService]);

  const loadAvailableSlots = async () => {
    setLoading(true);
    try {
      // Get available time slots for the selected date and staff
      const slots = appointmentService.getAvailableTimeSlots(selectedDate, selectedStaff);
      
      // Filter slots based on service duration if service is selected
      let filteredSlots = slots;
      if (selectedService) {
        const service = appointmentService.getServices().find(s => s.id === selectedService);
        if (service) {
          filteredSlots = slots.filter(slot => {
            // Check if there's enough time for the service duration
            const endTime = new Date(slot.getTime() + service.duration * 60000);
            const endHour = endTime.getHours();
            const endMinute = endTime.getMinutes();
            
            // Make sure service doesn't go past business hours
            if (endHour > TIME_SLOTS.END_HOUR || 
                (endHour === TIME_SLOTS.END_HOUR && endMinute > 0)) {
              return false;
            }
            
            // Make sure service doesn't conflict with lunch break
            if (endHour > TIME_SLOTS.BREAK_START && 
                slot.getHours() < TIME_SLOTS.BREAK_END) {
              return false;
            }
            
            // Check if the entire service duration is available
            return !appointmentService.hasTimeConflict(
              selectedStaff, 
              slot.toISOString(), 
              service.duration,
              excludeAppointmentId
            );
          });
        }
      }
      
      setAvailableSlots(filteredSlots);
    } catch (error) {
      console.error('Error loading available slots:', error);
      setAvailableSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isSlotSelected = (slot) => {
    if (!selectedTime) return false;
    return slot.getTime() === new Date(selectedTime).getTime();
  };

  const groupSlotsByTime = () => {
    const grouped = {};
    
    availableSlots.forEach(slot => {
      const hour = slot.getHours();
      const period = hour < 12 ? 'Morning' : hour < 17 ? 'Afternoon' : 'Evening';
      
      if (!grouped[period]) {
        grouped[period] = [];
      }
      grouped[period].push(slot);
    });
    
    return grouped;
  };

  const getServiceDuration = () => {
    if (!selectedService) return null;
    const service = appointmentService.getServices().find(s => s.id === selectedService);
    return service ? service.duration : null;
  };

  const getEndTime = (startTime) => {
    const duration = getServiceDuration();
    if (!duration) return null;
    
    const endTime = new Date(startTime.getTime() + duration * 60000);
    return formatTime(endTime);
  };

  if (!selectedDate || !selectedStaff) {
    return (
      <div className="time-slot-picker">
        <div className="no-selection">
          <p>Please select a date and staff member to view available time slots.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="time-slot-picker">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading available time slots...</p>
        </div>
      </div>
    );
  }

  const groupedSlots = groupSlotsByTime();
  const hasSlots = Object.keys(groupedSlots).length > 0;

  return (
    <div className="time-slot-picker">
      <div className="slot-picker-header">
        <h3>Available Time Slots</h3>
        <p className="selected-date">{formatDate(new Date(selectedDate))}</p>
        {selectedService && (
          <p className="service-duration">
            Service Duration: {getServiceDuration()} minutes
          </p>
        )}
      </div>

      {!hasSlots ? (
        <div className="no-slots">
          <p>No available time slots for the selected date and staff member.</p>
          <p>Please try a different date or staff member.</p>
        </div>
      ) : (
        <div className="time-slots-container">
          {Object.entries(groupedSlots).map(([period, slots]) => (
            <div key={period} className="time-period">
              <h4 className="period-title">{period}</h4>
              <div className="time-slots-grid">
                {slots.map((slot, index) => (
                  <button
                    key={index}
                    className={`time-slot ${isSlotSelected(slot) ? 'selected' : ''}`}
                    onClick={() => onTimeSelect && onTimeSelect(slot.toISOString())}
                  >
                    <div className="slot-time">
                      {formatTime(slot)}
                      {selectedService && (
                        <span className="slot-end-time">
                          - {getEndTime(slot)}
                        </span>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {hasSlots && (
        <div className="slot-picker-footer">
          <div className="slot-info">
            <div className="info-item">
              <span className="info-icon">🕒</span>
              <span>Business Hours: {TIME_SLOTS.START_HOUR}:00 AM - {TIME_SLOTS.END_HOUR}:00 PM</span>
            </div>
            <div className="info-item">
              <span className="info-icon">🍽️</span>
              <span>Lunch Break: {TIME_SLOTS.BREAK_START}:00 PM - {TIME_SLOTS.BREAK_END}:00 PM</span>
            </div>
            <div className="info-item">
              <span className="info-icon">⏱️</span>
              <span>Time Slot Duration: {TIME_SLOTS.DURATION} minutes</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeSlotPicker;
