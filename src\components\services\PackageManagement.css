/* Package Management Styles */
.package-management {
  width: 100%;
}

/* Package Header */
.packages-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-subtitle {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0;
}

/* Package Grid */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

/* Package Card */
.package-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: var(--space-8);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.package-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #8b5cf6, #3b82f6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-card:hover::before {
  opacity: 1;
}

.package-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.02), rgba(59, 130, 246, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.package-card:hover::after {
  opacity: 1;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.package-name {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.package-description {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.6;
  margin: 0 0 var(--space-4) 0;
}

/* Package Services */
.package-services {
  margin-bottom: var(--space-4);
}

.package-services h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--color-gray-700);
  margin: 0 0 var(--space-2) 0;
}

.services-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-item {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-gray-100);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.service-item:last-child {
  border-bottom: none;
}

/* Package Pricing */
.package-pricing {
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.pricing-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.original-price {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  text-decoration: line-through;
}

.package-price {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-green-600);
}

.discount-info {
  font-size: var(--text-sm);
  color: var(--color-green-600);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.duration-info {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
}

/* Package Validity */
.package-validity {
  font-size: var(--text-xs);
  color: var(--color-orange-600);
  background: var(--color-orange-100);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  display: inline-block;
  margin-bottom: var(--space-4);
}

/* Package Actions */
.package-actions {
  display: flex;
  gap: var(--space-2);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-gray-200);
}

/* No Packages State */
.no-packages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
}

.no-packages-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.no-packages h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.no-packages p {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0 0 var(--space-4) 0;
}

/* Package Form */
.package-form {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.package-form-content {
  width: 100%;
}

/* Services Selection */
.services-selection {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--space-3);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  background: white;
}

.service-option {
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.service-option:hover {
  background: var(--color-gray-50);
}

.service-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  cursor: pointer;
  width: 100%;
}

.service-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary-500);
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-1) 0;
}

.service-details {
  font-size: var(--text-xs);
  color: var(--color-gray-600);
  margin: 0;
}

/* Package Summary */
.package-summary {
  background: var(--color-primary-50);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.package-summary h4 {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--color-primary-700);
  margin: 0 0 var(--space-3) 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  font-size: var(--text-sm);
}

.summary-item.discount {
  font-weight: var(--font-semibold);
  color: var(--color-green-600);
  border-top: 1px solid var(--color-primary-200);
  margin-top: var(--space-2);
  padding-top: var(--space-3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .packages-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .packages-grid {
    grid-template-columns: 1fr;
  }

  .package-card {
    padding: var(--space-4);
  }

  .package-actions {
    flex-direction: column;
  }

  .pricing-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

@media (max-width: 480px) {
  .package-form {
    max-width: none;
  }

  .services-selection {
    max-height: 300px;
  }

  .service-checkbox {
    padding: var(--space-2);
  }

  .package-summary {
    padding: var(--space-3);
  }
}

/* Animation for package cards */
.package-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.service-option:hover .service-checkbox {
  transform: translateX(2px);
}

.package-card:hover .package-name {
  color: var(--color-primary-600);
}
