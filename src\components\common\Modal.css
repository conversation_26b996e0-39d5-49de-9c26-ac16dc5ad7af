/* Enhanced Modal System Styles */

/* Modal Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  padding: var(--space-4);
  opacity: 0;
  transition: all var(--transition-base);
}

.modal-backdrop.open {
  opacity: 1;
  animation: backdropFadeIn 0.15s ease-out;
}

.modal-backdrop.closing {
  opacity: 0;
  animation: backdropFadeOut 0.15s ease-in;
}

[data-theme="dark"] .modal-backdrop {
  background: rgba(0, 0, 0, 0.7);
}

/* Modal Content */
.modal-content {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--color-gray-200);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: var(--z-modal);
  transform: scale(0.9);
  opacity: 0;
  transition: all var(--transition-base);
}

.modal-content.open {
  transform: scale(1);
  opacity: 1;
}

.modal-content.closing {
  transform: scale(0.9);
  opacity: 0;
}

/* Modal Sizes */
.modal-content.sm {
  width: 100%;
  max-width: 400px;
}

.modal-content.md {
  width: 100%;
  max-width: 600px;
}

.modal-content.lg {
  width: 100%;
  max-width: 800px;
}

.modal-content.xl {
  width: 100%;
  max-width: 1200px;
}

.modal-content.full {
  width: 95vw;
  height: 95vh;
  max-width: none;
  max-height: none;
}

/* Modal Variants */
.modal-content.success {
  border-color: var(--color-success-200);
}

.modal-content.warning {
  border-color: var(--color-warning-200);
}

.modal-content.error,
.modal-content.danger {
  border-color: var(--color-error-200);
}

.modal-content.info {
  border-color: var(--color-info-200);
}

.modal-content.image {
  background: transparent;
  border: none;
  box-shadow: none;
  max-width: 90vw;
  max-height: 90vh;
}

/* Modal Animations */
.modal-content.scale {
  animation: modalScaleIn 0.15s ease-out;
}

.modal-content.scale.closing {
  animation: modalScaleOut 0.15s ease-in;
}

.modal-content.slide {
  animation: modalSlideIn 0.2s ease-out;
}

.modal-content.slide.closing {
  animation: modalSlideOut 0.2s ease-in;
}

.modal-content.fade {
  animation: modalFadeIn 0.2s ease-out;
}

.modal-content.fade.closing {
  animation: modalFadeOut 0.2s ease-in;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.modal-close-button {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close-button:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-600);
  transform: scale(1.1);
}

/* Modal Body */
.modal-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* Specific Modal Content Styles */
.confirm-modal-content {
  text-align: center;
}

.confirm-message {
  margin-bottom: var(--space-6);
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
}

.confirm-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
}

.alert-modal-content {
  text-align: center;
}

.alert-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-4);
}

.alert-message {
  margin-bottom: var(--space-6);
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
}

.alert-actions {
  display: flex;
  justify-content: center;
}

.form-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-modal-body {
  flex: 1;
  overflow-y: auto;
}

.form-modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding: var(--space-6);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

.image-modal-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-lg);
}

/* Drawer Styles */
.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.drawer-backdrop.open {
  opacity: 1;
}

.drawer-backdrop.closing {
  opacity: 0;
}

.drawer-content {
  position: fixed;
  top: 0;
  bottom: 0;
  background: white;
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  z-index: var(--z-modal);
  transition: transform var(--transition-base);
}

.drawer-content.right {
  right: 0;
  transform: translateX(100%);
}

.drawer-content.right.open {
  transform: translateX(0);
}

.drawer-content.right.closing {
  transform: translateX(100%);
}

.drawer-content.left {
  left: 0;
  transform: translateX(-100%);
}

.drawer-content.left.open {
  transform: translateX(0);
}

.drawer-content.left.closing {
  transform: translateX(-100%);
}

/* Drawer Sizes */
.drawer-content.sm {
  width: 320px;
}

.drawer-content.md {
  width: 480px;
}

.drawer-content.lg {
  width: 640px;
}

.drawer-content.xl {
  width: 800px;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

.drawer-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.drawer-close-button {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.drawer-close-button:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-600);
  transform: scale(1.1);
}

.drawer-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* Dark Mode Support */
[data-theme="dark"] .modal-content,
[data-theme="dark"] .drawer-content {
  background: var(--color-gray-800);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .drawer-header,
[data-theme="dark"] .form-modal-actions {
  background: var(--color-gray-900);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .modal-title,
[data-theme="dark"] .drawer-title {
  color: var(--color-gray-100);
}

[data-theme="dark"] .confirm-message,
[data-theme="dark"] .alert-message {
  color: var(--color-gray-300);
}

/* Animations */
@keyframes backdropFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes backdropFadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes modalScaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modalScaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.9);
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes modalSlideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalFadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: var(--space-2);
  }
  
  .modal-content.sm,
  .modal-content.md,
  .modal-content.lg,
  .modal-content.xl {
    width: 100%;
    max-width: none;
    margin: 0;
  }
  
  .modal-header,
  .modal-body,
  .form-modal-actions,
  .drawer-header,
  .drawer-body {
    padding: var(--space-4);
  }
  
  .confirm-actions,
  .form-modal-actions {
    flex-direction: column;
  }
  
  .drawer-content.sm,
  .drawer-content.md,
  .drawer-content.lg,
  .drawer-content.xl {
    width: 100%;
    max-width: 90vw;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .modal-backdrop,
  .modal-content,
  .drawer-backdrop,
  .drawer-content {
    transition: none;
    animation: none;
  }
  
  .modal-content.open,
  .drawer-content.open {
    transform: none;
    opacity: 1;
  }
}
