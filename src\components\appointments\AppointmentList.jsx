import React, { useState, useEffect } from 'react';
import appointmentService, { APPOINTMENT_STATUS } from '../../services/appointmentService';
import toast from 'react-hot-toast';
import './AppointmentList.css';

const AppointmentList = ({ 
  onAppointmentSelect, 
  onEditAppointment, 
  refreshTrigger,
  filterDate = null,
  filterStatus = null 
}) => {
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('asc');
  const [statusFilter, setStatusFilter] = useState(filterStatus || 'all');
  const [dateFilter, setDateFilter] = useState(filterDate || 'all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadAppointments();
  }, [refreshTrigger]);

  useEffect(() => {
    filterAndSortAppointments();
  }, [appointments, sortBy, sortOrder, statusFilter, dateFilter, searchTerm]);

  const loadAppointments = async () => {
    setLoading(true);
    try {
      const allAppointments = appointmentService.getAllAppointments();
      setAppointments(allAppointments);
    } catch (error) {
      console.error('Error loading appointments:', error);
      toast.error('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortAppointments = () => {
    let filtered = [...appointments];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(appointment =>
        appointment.customerName.toLowerCase().includes(term) ||
        appointment.serviceName.toLowerCase().includes(term) ||
        appointment.staffName.toLowerCase().includes(term) ||
        appointment.customerPhone.includes(term)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      switch (dateFilter) {
        case 'today':
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            appointmentDate.setHours(0, 0, 0, 0);
            return appointmentDate.getTime() === today.getTime();
          });
          break;
        case 'tomorrow':
          const tomorrow = new Date(today);
          tomorrow.setDate(today.getDate() + 1);
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            appointmentDate.setHours(0, 0, 0, 0);
            return appointmentDate.getTime() === tomorrow.getTime();
          });
          break;
        case 'this_week':
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          weekEnd.setHours(23, 59, 59, 999);
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            return appointmentDate >= weekStart && appointmentDate <= weekEnd;
          });
          break;
        case 'next_week':
          const nextWeekStart = new Date(today);
          nextWeekStart.setDate(today.getDate() - today.getDay() + 7);
          const nextWeekEnd = new Date(nextWeekStart);
          nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
          nextWeekEnd.setHours(23, 59, 59, 999);
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            return appointmentDate >= nextWeekStart && appointmentDate <= nextWeekEnd;
          });
          break;
        case 'past':
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            return appointmentDate < today;
          });
          break;
        case 'upcoming':
          filtered = filtered.filter(appointment => {
            const appointmentDate = new Date(appointment.date);
            return appointmentDate >= today;
          });
          break;
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'customer':
          aValue = a.customerName.toLowerCase();
          bValue = b.customerName.toLowerCase();
          break;
        case 'service':
          aValue = a.serviceName.toLowerCase();
          bValue = b.serviceName.toLowerCase();
          break;
        case 'staff':
          aValue = a.staffName.toLowerCase();
          bValue = b.staffName.toLowerCase();
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        default:
          aValue = a.date;
          bValue = b.date;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredAppointments(filtered);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleStatusChange = async (appointmentId, newStatus) => {
    try {
      await appointmentService.updateAppointment(appointmentId, { status: newStatus });
      loadAppointments();
      toast.success('Appointment status updated');
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast.error('Failed to update appointment status');
    }
  };

  const handleDeleteAppointment = async (appointmentId) => {
    if (!window.confirm('Are you sure you want to delete this appointment?')) {
      return;
    }

    try {
      await appointmentService.deleteAppointment(appointmentId);
      loadAppointments();
      toast.success('Appointment deleted successfully');
    } catch (error) {
      console.error('Error deleting appointment:', error);
      toast.error('Failed to delete appointment');
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case APPOINTMENT_STATUS.SCHEDULED:
        return '#3498db';
      case APPOINTMENT_STATUS.CONFIRMED:
        return '#2ecc71';
      case APPOINTMENT_STATUS.IN_PROGRESS:
        return '#f39c12';
      case APPOINTMENT_STATUS.COMPLETED:
        return '#27ae60';
      case APPOINTMENT_STATUS.CANCELLED:
        return '#e74c3c';
      case APPOINTMENT_STATUS.NO_SHOW:
        return '#95a5a6';
      default:
        return '#bdc3c7';
    }
  };

  const getStatusLabel = (status) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return '↕️';
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  if (loading) {
    return (
      <div className="appointment-list">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading appointments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-list">
      <div className="list-controls">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search appointments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filters">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Status</option>
            <option value={APPOINTMENT_STATUS.SCHEDULED}>Scheduled</option>
            <option value={APPOINTMENT_STATUS.CONFIRMED}>Confirmed</option>
            <option value={APPOINTMENT_STATUS.IN_PROGRESS}>In Progress</option>
            <option value={APPOINTMENT_STATUS.COMPLETED}>Completed</option>
            <option value={APPOINTMENT_STATUS.CANCELLED}>Cancelled</option>
            <option value={APPOINTMENT_STATUS.NO_SHOW}>No Show</option>
          </select>

          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Dates</option>
            <option value="today">Today</option>
            <option value="tomorrow">Tomorrow</option>
            <option value="this_week">This Week</option>
            <option value="next_week">Next Week</option>
            <option value="upcoming">Upcoming</option>
            <option value="past">Past</option>
          </select>
        </div>
      </div>

      <div className="appointments-table-container">
        <table className="appointments-table">
          <thead>
            <tr>
              <th onClick={() => handleSort('date')} className="sortable">
                Date & Time {getSortIcon('date')}
              </th>
              <th onClick={() => handleSort('customer')} className="sortable">
                Customer {getSortIcon('customer')}
              </th>
              <th onClick={() => handleSort('service')} className="sortable">
                Service {getSortIcon('service')}
              </th>
              <th onClick={() => handleSort('staff')} className="sortable">
                Staff {getSortIcon('staff')}
              </th>
              <th onClick={() => handleSort('status')} className="sortable">
                Status {getSortIcon('status')}
              </th>
              <th onClick={() => handleSort('price')} className="sortable">
                Price {getSortIcon('price')}
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredAppointments.length === 0 ? (
              <tr>
                <td colSpan="7" className="no-appointments">
                  No appointments found matching your criteria.
                </td>
              </tr>
            ) : (
              filteredAppointments.map(appointment => (
                <tr 
                  key={appointment.id} 
                  className="appointment-row"
                  onClick={() => onAppointmentSelect && onAppointmentSelect(appointment)}
                >
                  <td className="date-time">
                    <div className="date">{formatDate(appointment.date)}</div>
                    <div className="time">{formatTime(appointment.date)}</div>
                  </td>
                  <td className="customer">
                    <div className="customer-name">{appointment.customerName}</div>
                    <div className="customer-phone">{appointment.customerPhone}</div>
                  </td>
                  <td className="service">
                    <div className="service-name">{appointment.serviceName}</div>
                    <div className="service-duration">{appointment.duration} min</div>
                  </td>
                  <td className="staff">{appointment.staffName}</td>
                  <td className="status">
                    <select
                      value={appointment.status}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleStatusChange(appointment.id, e.target.value);
                      }}
                      className="status-select"
                      style={{ backgroundColor: getStatusColor(appointment.status) }}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <option value={APPOINTMENT_STATUS.SCHEDULED}>Scheduled</option>
                      <option value={APPOINTMENT_STATUS.CONFIRMED}>Confirmed</option>
                      <option value={APPOINTMENT_STATUS.IN_PROGRESS}>In Progress</option>
                      <option value={APPOINTMENT_STATUS.COMPLETED}>Completed</option>
                      <option value={APPOINTMENT_STATUS.CANCELLED}>Cancelled</option>
                      <option value={APPOINTMENT_STATUS.NO_SHOW}>No Show</option>
                    </select>
                  </td>
                  <td className="price">${appointment.price}</td>
                  <td className="actions">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditAppointment && onEditAppointment(appointment);
                      }}
                      className="action-btn edit-btn"
                      title="Edit Appointment"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAppointment(appointment.id);
                      }}
                      className="action-btn delete-btn"
                      title="Delete Appointment"
                    >
                      🗑️
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="list-summary">
        <p>Showing {filteredAppointments.length} of {appointments.length} appointments</p>
      </div>
    </div>
  );
};

export default AppointmentList;
