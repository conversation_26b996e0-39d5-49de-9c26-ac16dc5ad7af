/* Staff Performance Styles */
.staff-performance {
  padding: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
  background: var(--color-gray-50);
  min-height: 100vh;
}

.performance-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.header-info {
  flex: 1;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.title-icon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0;
}

.performance-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.coming-soon {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.7;
}

.coming-soon h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
}

.coming-soon p {
  color: var(--color-gray-600);
  font-size: var(--text-base);
  margin: 0 0 var(--space-4) 0;
}

.coming-soon ul {
  text-align: left;
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.6;
}

.coming-soon li {
  margin-bottom: var(--space-2);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
}

.btn-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.btn-secondary:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-900);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: var(--text-base);
}

@media (max-width: 768px) {
  .staff-performance {
    padding: var(--space-4);
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .page-title {
    font-size: var(--text-xl);
  }

  .title-icon {
    font-size: var(--text-2xl);
  }
}
