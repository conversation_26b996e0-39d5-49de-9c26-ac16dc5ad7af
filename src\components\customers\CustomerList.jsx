import React, { useState, useEffect } from 'react';
import customerService from '../../services/customerService';
import './CustomerList.css';

const CustomerList = ({ onSelectCustomer, onEditCustomer, onDeleteCustomer, refreshTrigger }) => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    sortBy: 'lastName',
    sortOrder: 'asc'
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    limit: 12
  });
  const [paginationData, setPaginationData] = useState(null);

  // Load customers
  const loadCustomers = async () => {
    setLoading(true);
    try {
      const result = customerService.getCustomersPaginated(
        pagination.currentPage,
        pagination.limit,
        searchQuery,
        filters
      );
      setCustomers(result.customers);
      setPaginationData(result.pagination);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load customers when dependencies change
  useEffect(() => {
    loadCustomers();
  }, [searchQuery, filters, pagination.currentPage, refreshTrigger]);

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page
  };

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({ ...prev, [filterName]: value }));
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    return status === 'active' ? 'status-badge active' : 'status-badge inactive';
  };

  if (loading) {
    return (
      <div className="customer-list-loading">
        <div className="loading-spinner"></div>
        <p>Loading customers...</p>
      </div>
    );
  }

  return (
    <div className="customer-list">
      {/* Search and Filters */}
      <div className="customer-list-header">
        <div className="search-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search customers by name, email, or phone..."
              value={searchQuery}
              onChange={handleSearch}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
        </div>

        <div className="filters-section">
          <div className="filter-group">
            <label>Status:</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="filter-select"
            >
              <option value="all">All Customers</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Sort by:</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="filter-select"
            >
              <option value="lastName">Last Name</option>
              <option value="firstName">First Name</option>
              <option value="email">Email</option>
              <option value="lastVisit">Last Visit</option>
              <option value="totalSpent">Total Spent</option>
              <option value="totalVisits">Total Visits</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Order:</label>
            <select
              value={filters.sortOrder}
              onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
              className="filter-select"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      {paginationData && (
        <div className="results-summary">
          <p>
            Showing {customers.length} of {paginationData.totalCustomers} customers
            {searchQuery && ` for "${searchQuery}"`}
          </p>
        </div>
      )}

      {/* Customer Cards */}
      <div className="customer-cards">
        {customers.length === 0 ? (
          <div className="no-customers">
            <div className="no-customers-icon">👥</div>
            <h3>No customers found</h3>
            <p>
              {searchQuery 
                ? `No customers match your search for "${searchQuery}"`
                : 'No customers have been added yet'
              }
            </p>
          </div>
        ) : (
          customers.map(customer => (
            <div key={customer.id} className="customer-card">
              <div className="customer-card-header">
                <div className="customer-info">
                  <h3 className="customer-name">
                    {customer.firstName} {customer.lastName}
                  </h3>
                  <span className={getStatusBadgeClass(customer.status)}>
                    {customer.status}
                  </span>
                </div>
                <div className="customer-actions">
                  <button
                    onClick={() => onSelectCustomer(customer)}
                    className="action-btn view-btn"
                    title="View Details"
                  >
                    👁️
                  </button>
                  <button
                    onClick={() => onEditCustomer(customer)}
                    className="action-btn edit-btn"
                    title="Edit Customer"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDeleteCustomer(customer)}
                    className="action-btn delete-btn"
                    title="Delete Customer"
                  >
                    🗑️
                  </button>
                </div>
              </div>

              <div className="customer-card-body">
                <div className="contact-info">
                  <div className="contact-item">
                    <span className="contact-icon">📧</span>
                    <span className="contact-text">{customer.email}</span>
                  </div>
                  <div className="contact-item">
                    <span className="contact-icon">📞</span>
                    <span className="contact-text">{customer.phone}</span>
                  </div>
                </div>

                <div className="customer-stats">
                  <div className="stat-item">
                    <span className="stat-label">Total Visits:</span>
                    <span className="stat-value">{customer.totalVisits}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Total Spent:</span>
                    <span className="stat-value">{formatCurrency(customer.totalSpent)}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Last Visit:</span>
                    <span className="stat-value">{formatDate(customer.lastVisit)}</span>
                  </div>
                </div>

                {customer.preferences.preferredServices.length > 0 && (
                  <div className="preferred-services">
                    <span className="services-label">Preferred Services:</span>
                    <div className="services-tags">
                      {customer.preferences.preferredServices.slice(0, 2).map((service, index) => (
                        <span key={index} className="service-tag">{service}</span>
                      ))}
                      {customer.preferences.preferredServices.length > 2 && (
                        <span className="service-tag more">
                          +{customer.preferences.preferredServices.length - 2} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {paginationData && paginationData.totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(paginationData.currentPage - 1)}
            disabled={!paginationData.hasPrevPage}
            className="pagination-btn"
          >
            ← Previous
          </button>

          <div className="pagination-info">
            <span>
              Page {paginationData.currentPage} of {paginationData.totalPages}
            </span>
          </div>

          <button
            onClick={() => handlePageChange(paginationData.currentPage + 1)}
            disabled={!paginationData.hasNextPage}
            className="pagination-btn"
          >
            Next →
          </button>
        </div>
      )}
    </div>
  );
};

export default CustomerList;
