// Staff Management Service for Salon Management System
import { v4 as uuidv4 } from 'uuid';

// Staff Status
export const STAFF_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ON_LEAVE: 'on_leave',
  TERMINATED: 'terminated'
};

// Staff Roles
export const STAFF_ROLES = {
  STYLIST: 'stylist',
  BEAUTICIAN: 'beautician',
  NAIL_TECHNICIAN: 'nail_technician',
  MASSAGE_THERAPIST: 'massage_therapist',
  RECEPTIONIST: 'receptionist',
  MANAGER: 'manager'
};

// Skill Levels
export const SKILL_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  EXPERT: 'expert'
};

// Commission Types
export const COMMISSION_TYPES = {
  PERCENTAGE: 'percentage',
  FIXED: 'fixed',
  TIERED: 'tiered'
};

class StaffService {
  constructor() {
    this.STORAGE_KEY = 'salon_staff';
    this.SCHEDULES_KEY = 'salon_staff_schedules';
    this.PERFORMANCE_KEY = 'salon_staff_performance';
    this.staff = this.loadStaff();
    this.schedules = this.loadSchedules();
    this.performance = this.loadPerformance();
    this.nextId = this.getNextId();
  }

  // Load staff from localStorage or generate mock data
  loadStaff() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
    }
    
    // Generate initial mock data
    const mockStaff = this.generateMockStaff();
    this.saveStaff(mockStaff);
    return mockStaff;
  }

  // Load schedules from localStorage or generate mock data
  loadSchedules() {
    try {
      const stored = localStorage.getItem(this.SCHEDULES_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading schedules:', error);
    }
    
    const mockSchedules = this.generateMockSchedules();
    this.saveSchedules(mockSchedules);
    return mockSchedules;
  }

  // Load performance data from localStorage or generate mock data
  loadPerformance() {
    try {
      const stored = localStorage.getItem(this.PERFORMANCE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading performance:', error);
    }
    
    const mockPerformance = this.generateMockPerformance();
    this.savePerformance(mockPerformance);
    return mockPerformance;
  }

  // Generate mock staff data
  generateMockStaff() {
    return [
      {
        id: 'staff_1',
        employeeId: 'EMP001',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '(*************',
        role: STAFF_ROLES.STYLIST,
        status: STAFF_STATUS.ACTIVE,
        hireDate: '2023-01-15',
        dateOfBirth: '1990-05-20',
        address: {
          street: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345'
        },
        skills: [
          { serviceId: 'service_1', level: SKILL_LEVELS.EXPERT },
          { serviceId: 'service_3', level: SKILL_LEVELS.ADVANCED },
          { serviceId: 'service_4', level: SKILL_LEVELS.ADVANCED }
        ],
        specialties: ['haircut', 'hair_color', 'highlights'],
        commission: {
          type: COMMISSION_TYPES.PERCENTAGE,
          rate: 40,
          minimumSales: 1000
        },
        hourlyRate: 25,
        avatar: null,
        notes: 'Excellent with color treatments and customer service',
        createdAt: new Date('2023-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'staff_2',
        employeeId: 'EMP002',
        firstName: 'Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        phone: '(*************',
        role: STAFF_ROLES.BEAUTICIAN,
        status: STAFF_STATUS.ACTIVE,
        hireDate: '2023-03-10',
        dateOfBirth: '1988-08-15',
        address: {
          street: '456 Oak Ave',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345'
        },
        skills: [
          { serviceId: 'service_6', level: SKILL_LEVELS.EXPERT },
          { serviceId: 'service_7', level: SKILL_LEVELS.ADVANCED },
          { serviceId: 'service_10', level: SKILL_LEVELS.INTERMEDIATE }
        ],
        specialties: ['facial', 'eyebrow', 'massage'],
        commission: {
          type: COMMISSION_TYPES.PERCENTAGE,
          rate: 35,
          minimumSales: 800
        },
        hourlyRate: 22,
        avatar: null,
        notes: 'Specializes in facial treatments and eyebrow shaping',
        createdAt: new Date('2023-03-10').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'staff_3',
        employeeId: 'EMP003',
        firstName: 'Lisa',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '(*************',
        role: STAFF_ROLES.NAIL_TECHNICIAN,
        status: STAFF_STATUS.ACTIVE,
        hireDate: '2023-06-01',
        dateOfBirth: '1992-12-03',
        address: {
          street: '789 Pine St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345'
        },
        skills: [
          { serviceId: 'service_8', level: SKILL_LEVELS.EXPERT },
          { serviceId: 'service_9', level: SKILL_LEVELS.EXPERT }
        ],
        specialties: ['manicure', 'pedicure'],
        commission: {
          type: COMMISSION_TYPES.FIXED,
          rate: 15,
          minimumSales: 0
        },
        hourlyRate: 20,
        avatar: null,
        notes: 'Expert in nail art and gel applications',
        createdAt: new Date('2023-06-01').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'staff_4',
        employeeId: 'EMP004',
        firstName: 'Emma',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '(*************',
        role: STAFF_ROLES.STYLIST,
        status: STAFF_STATUS.ACTIVE,
        hireDate: '2023-09-15',
        dateOfBirth: '1995-04-18',
        address: {
          street: '321 Elm St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345'
        },
        skills: [
          { serviceId: 'service_1', level: SKILL_LEVELS.ADVANCED },
          { serviceId: 'service_2', level: SKILL_LEVELS.EXPERT },
          { serviceId: 'service_5', level: SKILL_LEVELS.INTERMEDIATE }
        ],
        specialties: ['haircut', 'hair_wash', 'perm'],
        commission: {
          type: COMMISSION_TYPES.PERCENTAGE,
          rate: 30,
          minimumSales: 600
        },
        hourlyRate: 18,
        avatar: null,
        notes: 'Great with classic cuts and styling',
        createdAt: new Date('2023-09-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      }
    ];
  }

  // Generate mock schedules
  generateMockSchedules() {
    const schedules = {};
    this.staff.forEach(staff => {
      schedules[staff.id] = {
        staffId: staff.id,
        workingHours: {
          monday: { start: '09:00', end: '17:00', isWorking: true },
          tuesday: { start: '09:00', end: '17:00', isWorking: true },
          wednesday: { start: '09:00', end: '17:00', isWorking: true },
          thursday: { start: '09:00', end: '17:00', isWorking: true },
          friday: { start: '09:00', end: '17:00', isWorking: true },
          saturday: { start: '10:00', end: '16:00', isWorking: true },
          sunday: { start: '10:00', end: '16:00', isWorking: false }
        },
        timeOff: [],
        availability: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });
    return schedules;
  }

  // Generate mock performance data
  generateMockPerformance() {
    const performance = {};
    this.staff.forEach(staff => {
      performance[staff.id] = {
        staffId: staff.id,
        monthlyStats: {
          appointmentsCompleted: Math.floor(Math.random() * 50) + 20,
          totalRevenue: Math.floor(Math.random() * 3000) + 1000,
          averageRating: (Math.random() * 1.5 + 3.5).toFixed(1),
          customerRetention: Math.floor(Math.random() * 30) + 70,
          commissionEarned: Math.floor(Math.random() * 800) + 200
        },
        yearlyStats: {
          appointmentsCompleted: Math.floor(Math.random() * 500) + 200,
          totalRevenue: Math.floor(Math.random() * 30000) + 10000,
          averageRating: (Math.random() * 1.5 + 3.5).toFixed(1),
          customerRetention: Math.floor(Math.random() * 30) + 70,
          commissionEarned: Math.floor(Math.random() * 8000) + 2000
        },
        goals: {
          monthlyRevenue: 2500,
          monthlyAppointments: 60,
          customerSatisfaction: 4.5
        },
        reviews: [
          {
            id: uuidv4(),
            customerId: 'customer_1',
            rating: 5,
            comment: 'Excellent service, very professional',
            date: new Date().toISOString()
          }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });
    return performance;
  }

  // Save methods
  saveStaff(staff = this.staff) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(staff));
    } catch (error) {
      console.error('Error saving staff:', error);
    }
  }

  saveSchedules(schedules = this.schedules) {
    try {
      localStorage.setItem(this.SCHEDULES_KEY, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error saving schedules:', error);
    }
  }

  savePerformance(performance = this.performance) {
    try {
      localStorage.setItem(this.PERFORMANCE_KEY, JSON.stringify(performance));
    } catch (error) {
      console.error('Error saving performance:', error);
    }
  }

  // Get next ID for new staff
  getNextId() {
    const maxId = Math.max(
      ...this.staff.map(s => parseInt(s.id.replace('staff_', '')) || 0),
      0
    );
    return maxId + 1;
  }

  // Staff CRUD operations
  getAllStaff() {
    return [...this.staff];
  }

  getStaffById(id) {
    return this.staff.find(staff => staff.id === id);
  }

  getActiveStaff() {
    return this.staff.filter(staff => staff.status === STAFF_STATUS.ACTIVE);
  }

  getStaffByRole(role) {
    return this.staff.filter(staff => staff.role === role);
  }

  searchStaff(query) {
    const searchTerm = query.toLowerCase();
    return this.staff.filter(staff =>
      staff.firstName.toLowerCase().includes(searchTerm) ||
      staff.lastName.toLowerCase().includes(searchTerm) ||
      staff.email.toLowerCase().includes(searchTerm) ||
      staff.employeeId.toLowerCase().includes(searchTerm)
    );
  }

  addStaff(staffData) {
    const newStaff = {
      id: `staff_${this.nextId}`,
      employeeId: staffData.employeeId || `EMP${String(this.nextId).padStart(3, '0')}`,
      ...staffData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.staff.push(newStaff);
    this.nextId++;
    this.saveStaff();

    // Initialize schedule and performance for new staff
    this.schedules[newStaff.id] = this.createDefaultSchedule(newStaff.id);
    this.performance[newStaff.id] = this.createDefaultPerformance(newStaff.id);
    this.saveSchedules();
    this.savePerformance();

    return newStaff;
  }

  updateStaff(id, updates) {
    const index = this.staff.findIndex(staff => staff.id === id);
    if (index === -1) {
      throw new Error('Staff member not found');
    }

    this.staff[index] = {
      ...this.staff[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveStaff();
    return this.staff[index];
  }

  deleteStaff(id) {
    const index = this.staff.findIndex(staff => staff.id === id);
    if (index === -1) {
      throw new Error('Staff member not found');
    }

    this.staff.splice(index, 1);
    delete this.schedules[id];
    delete this.performance[id];
    
    this.saveStaff();
    this.saveSchedules();
    this.savePerformance();
    return true;
  }

  // Schedule operations
  getStaffSchedule(staffId) {
    return this.schedules[staffId];
  }

  updateStaffSchedule(staffId, scheduleData) {
    this.schedules[staffId] = {
      ...this.schedules[staffId],
      ...scheduleData,
      updatedAt: new Date().toISOString()
    };
    this.saveSchedules();
    return this.schedules[staffId];
  }

  // Performance operations
  getStaffPerformance(staffId) {
    return this.performance[staffId];
  }

  updateStaffPerformance(staffId, performanceData) {
    this.performance[staffId] = {
      ...this.performance[staffId],
      ...performanceData,
      updatedAt: new Date().toISOString()
    };
    this.savePerformance();
    return this.performance[staffId];
  }

  // Helper methods
  createDefaultSchedule(staffId) {
    return {
      staffId,
      workingHours: {
        monday: { start: '09:00', end: '17:00', isWorking: true },
        tuesday: { start: '09:00', end: '17:00', isWorking: true },
        wednesday: { start: '09:00', end: '17:00', isWorking: true },
        thursday: { start: '09:00', end: '17:00', isWorking: true },
        friday: { start: '09:00', end: '17:00', isWorking: true },
        saturday: { start: '10:00', end: '16:00', isWorking: true },
        sunday: { start: '10:00', end: '16:00', isWorking: false }
      },
      timeOff: [],
      availability: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  createDefaultPerformance(staffId) {
    return {
      staffId,
      monthlyStats: {
        appointmentsCompleted: 0,
        totalRevenue: 0,
        averageRating: 0,
        customerRetention: 0,
        commissionEarned: 0
      },
      yearlyStats: {
        appointmentsCompleted: 0,
        totalRevenue: 0,
        averageRating: 0,
        customerRetention: 0,
        commissionEarned: 0
      },
      goals: {
        monthlyRevenue: 2000,
        monthlyAppointments: 50,
        customerSatisfaction: 4.0
      },
      reviews: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  // Statistics
  getStaffStats() {
    const totalStaff = this.staff.length;
    const activeStaff = this.staff.filter(s => s.status === STAFF_STATUS.ACTIVE).length;
    const onLeave = this.staff.filter(s => s.status === STAFF_STATUS.ON_LEAVE).length;

    const roleStats = {};
    Object.values(STAFF_ROLES).forEach(role => {
      roleStats[role] = this.staff.filter(s => s.role === role).length;
    });

    const avgHourlyRate = this.staff.reduce((sum, staff) => sum + (staff.hourlyRate || 0), 0) / totalStaff;

    return {
      totalStaff,
      activeStaff,
      onLeave,
      roleStats,
      avgHourlyRate: Math.round(avgHourlyRate * 100) / 100
    };
  }

  // Get staff availability for a specific date
  getStaffAvailability(staffId, date) {
    const schedule = this.schedules[staffId];
    if (!schedule) return null;

    const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' });
    const workingHours = schedule.workingHours[dayOfWeek];

    if (!workingHours || !workingHours.isWorking) {
      return null;
    }

    // Check for time off
    const isOnTimeOff = schedule.timeOff.some(timeOff => {
      const startDate = new Date(timeOff.startDate);
      const endDate = new Date(timeOff.endDate);
      const checkDate = new Date(date);
      return checkDate >= startDate && checkDate <= endDate;
    });

    if (isOnTimeOff) {
      return null;
    }

    return workingHours;
  }

  // Get staff for a specific service
  getStaffForService(serviceId) {
    return this.staff.filter(staff =>
      staff.status === STAFF_STATUS.ACTIVE &&
      staff.specialties.includes(serviceId)
    );
  }
}

// Create and export singleton instance
const staffService = new StaffService();
export default staffService;
