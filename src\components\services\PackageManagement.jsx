import React, { useState } from 'react';
import serviceService, { SERVICE_STATUS } from '../../services/serviceService';
import toast from 'react-hot-toast';
import './PackageManagement.css';

const PackageManagement = ({ packages, services, onRefresh, canEdit }) => {
  const [showForm, setShowForm] = useState(false);
  const [editingPackage, setEditingPackage] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    services: [],
    packagePrice: 0,
    validUntil: '',
    status: SERVICE_STATUS.ACTIVE
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const activeServices = services.filter(service => service.status === SERVICE_STATUS.ACTIVE);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      services: [],
      packagePrice: 0,
      validUntil: '',
      status: SERVICE_STATUS.ACTIVE
    });
    setErrors({});
    setEditingPackage(null);
  };

  const handleAddPackage = () => {
    resetForm();
    setShowForm(true);
  };

  const handleEditPackage = (pkg) => {
    setFormData({
      name: pkg.name,
      description: pkg.description,
      services: pkg.services,
      packagePrice: pkg.packagePrice,
      validUntil: pkg.validUntil ? pkg.validUntil.split('T')[0] : '',
      status: pkg.status
    });
    setEditingPackage(pkg);
    setShowForm(true);
  };

  const handleDeletePackage = async (packageId) => {
    if (!window.confirm('Are you sure you want to delete this package?')) {
      return;
    }

    try {
      await serviceService.deletePackage(packageId);
      toast.success('Package deleted successfully');
      onRefresh();
    } catch (error) {
      console.error('Error deleting package:', error);
      toast.error('Failed to delete package');
    }
  };

  const calculateOriginalPrice = () => {
    return formData.services.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.price : 0);
    }, 0);
  };

  const calculateDiscount = () => {
    const originalPrice = calculateOriginalPrice();
    return originalPrice - formData.packagePrice;
  };

  const calculateDiscountPercentage = () => {
    const originalPrice = calculateOriginalPrice();
    if (originalPrice === 0) return 0;
    return Math.round((calculateDiscount() / originalPrice) * 100);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Package name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Package description is required';
    }

    if (formData.services.length === 0) {
      newErrors.services = 'At least one service must be selected';
    }

    if (formData.packagePrice <= 0) {
      newErrors.packagePrice = 'Package price must be greater than 0';
    }

    const originalPrice = calculateOriginalPrice();
    if (formData.packagePrice >= originalPrice) {
      newErrors.packagePrice = 'Package price should be less than original price for a discount';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const packageData = {
        ...formData,
        originalPrice: calculateOriginalPrice(),
        discount: calculateDiscount(),
        duration: formData.services.reduce((total, serviceId) => {
          const service = services.find(s => s.id === serviceId);
          return total + (service ? service.duration : 0);
        }, 0),
        validUntil: formData.validUntil ? new Date(formData.validUntil).toISOString() : null
      };

      if (editingPackage) {
        await serviceService.updatePackage(editingPackage.id, packageData);
        toast.success('Package updated successfully');
      } else {
        await serviceService.addPackage(packageData);
        toast.success('Package created successfully');
      }

      setShowForm(false);
      resetForm();
      onRefresh();
    } catch (error) {
      console.error('Error saving package:', error);
      toast.error('Failed to save package');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleServiceToggle = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`;
    }
    return `${mins}m`;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      [SERVICE_STATUS.ACTIVE]: { label: 'Active', className: 'status-active' },
      [SERVICE_STATUS.INACTIVE]: { label: 'Inactive', className: 'status-inactive' },
      [SERVICE_STATUS.DISCONTINUED]: { label: 'Discontinued', className: 'status-discontinued' }
    };

    const config = statusConfig[status] || statusConfig[SERVICE_STATUS.ACTIVE];
    return <span className={`status-badge ${config.className}`}>{config.label}</span>;
  };

  if (showForm) {
    return (
      <div className="package-form">
        <div className="form-header">
          <h2 className="form-title">
            <span className="title-icon">
              {editingPackage ? '✏️' : '🎁'}
            </span>
            {editingPackage ? 'Edit Package' : 'Create New Package'}
          </h2>
          <button
            type="button"
            onClick={() => setShowForm(false)}
            className="btn btn-secondary"
          >
            Back to Packages
          </button>
        </div>

        <form onSubmit={handleSubmit} className="package-form-content">
          <div className="form-grid">
            <div className="form-section">
              <h3 className="section-title">Package Information</h3>
              
              <div className="form-group">
                <label htmlFor="name" className="form-label">Package Name *</label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`form-input ${errors.name ? 'form-input-error' : ''}`}
                  placeholder="Enter package name"
                />
                {errors.name && <span className="error-message">{errors.name}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="description" className="form-label">Description *</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className={`form-textarea ${errors.description ? 'form-input-error' : ''}`}
                  placeholder="Describe the package"
                  rows="3"
                />
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="packagePrice" className="form-label">Package Price ($) *</label>
                  <input
                    type="number"
                    id="packagePrice"
                    value={formData.packagePrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, packagePrice: parseFloat(e.target.value) || 0 }))}
                    className={`form-input ${errors.packagePrice ? 'form-input-error' : ''}`}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                  {errors.packagePrice && <span className="error-message">{errors.packagePrice}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="validUntil" className="form-label">Valid Until</label>
                  <input
                    type="date"
                    id="validUntil"
                    value={formData.validUntil}
                    onChange={(e) => setFormData(prev => ({ ...prev, validUntil: e.target.value }))}
                    className="form-input"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="status" className="form-label">Status</label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  className="form-select"
                >
                  <option value={SERVICE_STATUS.ACTIVE}>Active</option>
                  <option value={SERVICE_STATUS.INACTIVE}>Inactive</option>
                  <option value={SERVICE_STATUS.DISCONTINUED}>Discontinued</option>
                </select>
              </div>
            </div>

            <div className="form-section">
              <h3 className="section-title">Select Services</h3>
              {errors.services && <span className="error-message">{errors.services}</span>}
              
              <div className="services-selection">
                {activeServices.map(service => (
                  <div key={service.id} className="service-option">
                    <label className="service-checkbox">
                      <input
                        type="checkbox"
                        checked={formData.services.includes(service.id)}
                        onChange={() => handleServiceToggle(service.id)}
                      />
                      <div className="service-info">
                        <div className="service-name">{service.name}</div>
                        <div className="service-details">
                          ${service.price} • {formatDuration(service.duration)}
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>

              {formData.services.length > 0 && (
                <div className="package-summary">
                  <h4>Package Summary</h4>
                  <div className="summary-item">
                    <span>Original Price:</span>
                    <span>${calculateOriginalPrice()}</span>
                  </div>
                  <div className="summary-item">
                    <span>Package Price:</span>
                    <span>${formData.packagePrice}</span>
                  </div>
                  <div className="summary-item discount">
                    <span>Discount:</span>
                    <span>${calculateDiscount()} ({calculateDiscountPercentage()}%)</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="form-actions">
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="btn btn-secondary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="btn-spinner"></span>
                  {editingPackage ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <span className="btn-icon">💾</span>
                  {editingPackage ? 'Update Package' : 'Create Package'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    );
  }

  return (
    <div className="package-management">
      <div className="packages-header">
        <div className="header-content">
          <h2 className="section-title">
            <span className="title-icon">🎁</span>
            Service Packages
          </h2>
          <p className="section-subtitle">
            Create and manage service packages and deals
          </p>
        </div>
        
        {canEdit && (
          <button
            className="btn btn-primary"
            onClick={handleAddPackage}
          >
            <span className="btn-icon">➕</span>
            Add Package
          </button>
        )}
      </div>

      {packages.length === 0 ? (
        <div className="no-packages">
          <div className="no-packages-icon">🎁</div>
          <h3>No packages created yet</h3>
          <p>Create your first service package to offer deals to customers</p>
          {canEdit && (
            <button
              className="btn btn-primary"
              onClick={handleAddPackage}
            >
              <span className="btn-icon">➕</span>
              Create Package
            </button>
          )}
        </div>
      ) : (
        <div className="packages-grid">
          {packages.map(pkg => (
            <div key={pkg.id} className="package-card">
              <div className="package-header">
                <h3 className="package-name">{pkg.name}</h3>
                {getStatusBadge(pkg.status)}
              </div>

              <p className="package-description">{pkg.description}</p>

              <div className="package-services">
                <h4>Included Services:</h4>
                <ul className="services-list">
                  {pkg.services.map(serviceId => {
                    const service = services.find(s => s.id === serviceId);
                    return service ? (
                      <li key={serviceId} className="service-item">
                        {service.name} - ${service.price}
                      </li>
                    ) : null;
                  })}
                </ul>
              </div>

              <div className="package-pricing">
                <div className="pricing-row">
                  <span className="original-price">${pkg.originalPrice}</span>
                  <span className="package-price">${pkg.packagePrice}</span>
                </div>
                <div className="discount-info">
                  Save ${pkg.discount} ({Math.round((pkg.discount / pkg.originalPrice) * 100)}%)
                </div>
                <div className="duration-info">
                  Total Duration: {formatDuration(pkg.duration)}
                </div>
              </div>

              {pkg.validUntil && (
                <div className="package-validity">
                  Valid until: {new Date(pkg.validUntil).toLocaleDateString()}
                </div>
              )}

              {canEdit && (
                <div className="package-actions">
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={() => handleEditPackage(pkg)}
                  >
                    <span className="btn-icon">✏️</span>
                    Edit
                  </button>
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => handleDeletePackage(pkg.id)}
                  >
                    <span className="btn-icon">🗑️</span>
                    Delete
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PackageManagement;
