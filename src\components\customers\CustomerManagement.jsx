import React, { useState, useEffect } from 'react';
import CustomerList from './CustomerList';
import CustomerForm from './CustomerForm';
import CustomerDetail from './CustomerDetail';
import customerService from '../../services/customerService';
import toast from 'react-hot-toast';
import './CustomerManagement.css';

const CustomerManagement = () => {
  const [currentView, setCurrentView] = useState('list'); // 'list', 'add', 'edit', 'detail'
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [stats, setStats] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);

  // Load customer statistics
  useEffect(() => {
    const loadStats = () => {
      const customerStats = customerService.getCustomerStats();
      setStats(customerStats);
    };

    loadStats();
  }, [refreshTrigger]);

  // Handle view changes
  const handleViewChange = (view, customer = null) => {
    setCurrentView(view);
    setSelectedCustomer(customer);
  };

  // Handle customer selection for viewing details
  const handleSelectCustomer = (customer) => {
    setSelectedCustomer(customer);
    setCurrentView('detail');
  };

  // Handle customer editing
  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setCurrentView('edit');
  };

  // Handle customer deletion
  const handleDeleteCustomer = (customer) => {
    setCustomerToDelete(customer);
    setShowDeleteConfirm(true);
  };

  // Confirm customer deletion
  const confirmDeleteCustomer = () => {
    if (customerToDelete) {
      const result = customerService.deleteCustomer(customerToDelete.id);
      
      if (result.success) {
        toast.success('Customer deleted successfully!');
        setRefreshTrigger(prev => prev + 1);
        
        // If we're viewing the deleted customer, go back to list
        if (selectedCustomer && selectedCustomer.id === customerToDelete.id) {
          setCurrentView('list');
          setSelectedCustomer(null);
        }
      } else {
        toast.error(result.error || 'Failed to delete customer');
      }
    }
    
    setShowDeleteConfirm(false);
    setCustomerToDelete(null);
  };

  // Cancel customer deletion
  const cancelDeleteCustomer = () => {
    setShowDeleteConfirm(false);
    setCustomerToDelete(null);
  };

  // Handle successful customer save
  const handleCustomerSave = (customer) => {
    setRefreshTrigger(prev => prev + 1);
    setCurrentView('list');
    setSelectedCustomer(null);
  };

  // Handle cancel operations
  const handleCancel = () => {
    setCurrentView('list');
    setSelectedCustomer(null);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="customer-management">
      {/* Header */}
      <div className="customer-management-header">
        <div className="header-content">
          <h1>Customer Management</h1>
          <p>Manage your salon's customer database</p>
        </div>
        
        {currentView === 'list' && (
          <button
            onClick={() => handleViewChange('add')}
            className="btn btn-primary add-customer-btn"
          >
            ➕ Add New Customer
          </button>
        )}
      </div>

      {/* Statistics Cards */}
      {currentView === 'list' && stats && (
        <div className="customer-stats">
          <div className="stat-card">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <h3>{stats.totalCustomers}</h3>
              <p>Total Customers</p>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <h3>{stats.activeCustomers}</h3>
              <p>Active Customers</p>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.totalRevenue)}</h3>
              <p>Total Revenue</p>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">📈</div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.averageSpending)}</h3>
              <p>Avg. per Customer</p>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">🆕</div>
            <div className="stat-content">
              <h3>{stats.recentCustomers}</h3>
              <p>New This Month</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="customer-management-content">
        {/* Customer List View */}
        {currentView === 'list' && (
          <CustomerList
            onSelectCustomer={handleSelectCustomer}
            onEditCustomer={handleEditCustomer}
            onDeleteCustomer={handleDeleteCustomer}
            refreshTrigger={refreshTrigger}
          />
        )}

        {/* Add Customer Form */}
        {currentView === 'add' && (
          <CustomerForm
            onSave={handleCustomerSave}
            onCancel={handleCancel}
            isEditing={false}
          />
        )}

        {/* Edit Customer Form */}
        {currentView === 'edit' && selectedCustomer && (
          <CustomerForm
            customer={selectedCustomer}
            onSave={handleCustomerSave}
            onCancel={handleCancel}
            isEditing={true}
          />
        )}

        {/* Customer Detail View */}
        {currentView === 'detail' && selectedCustomer && (
          <CustomerDetail
            customer={selectedCustomer}
            onClose={handleCancel}
            onEdit={handleEditCustomer}
            onDelete={handleDeleteCustomer}
          />
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && customerToDelete && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Confirm Deletion</h3>
            </div>
            
            <div className="modal-body">
              <p>
                Are you sure you want to delete customer{' '}
                <strong>{customerToDelete.firstName} {customerToDelete.lastName}</strong>?
              </p>
              <p className="warning-text">
                This action cannot be undone. All customer data and history will be permanently removed.
              </p>
            </div>
            
            <div className="modal-actions">
              <button
                onClick={cancelDeleteCustomer}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteCustomer}
                className="btn btn-danger"
              >
                Delete Customer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerManagement;
