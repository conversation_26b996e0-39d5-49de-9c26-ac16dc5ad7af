import React, { useState } from 'react';
import './CustomerDetail.css';

const CustomerDetail = ({ customer, onClose, onEdit, onDelete }) => {
  const [activeTab, setActiveTab] = useState('profile');

  if (!customer) return null;

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format date with time
  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate age
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return 'Not provided';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return `${age} years old`;
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    return status === 'active' ? 'status-badge active' : 'status-badge inactive';
  };

  return (
    <div className="customer-detail-overlay">
      <div className="customer-detail-container">
        {/* Header */}
        <div className="customer-detail-header">
          <div className="customer-header-info">
            <div className="customer-avatar">
              {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
            </div>
            <div className="customer-title">
              <h2>{customer.firstName} {customer.lastName}</h2>
              <span className={getStatusBadgeClass(customer.status)}>
                {customer.status}
              </span>
            </div>
          </div>
          
          <div className="customer-header-actions">
            <button
              onClick={() => onEdit(customer)}
              className="btn btn-primary"
            >
              ✏️ Edit
            </button>
            <button
              onClick={() => onDelete(customer)}
              className="btn btn-danger"
            >
              🗑️ Delete
            </button>
            <button
              onClick={onClose}
              className="btn btn-secondary close-btn"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="customer-quick-stats">
          <div className="stat-card">
            <div className="stat-icon">📅</div>
            <div className="stat-info">
              <span className="stat-value">{customer.totalVisits}</span>
              <span className="stat-label">Total Visits</span>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-info">
              <span className="stat-value">{formatCurrency(customer.totalSpent)}</span>
              <span className="stat-label">Total Spent</span>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">🕒</div>
            <div className="stat-info">
              <span className="stat-value">{formatDate(customer.lastVisit)}</span>
              <span className="stat-label">Last Visit</span>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">💵</div>
            <div className="stat-info">
              <span className="stat-value">
                {customer.totalVisits > 0 ? formatCurrency(customer.totalSpent / customer.totalVisits) : '$0'}
              </span>
              <span className="stat-label">Avg. per Visit</span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="customer-detail-tabs">
          <button
            className={`tab-btn ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            👤 Profile
          </button>
          <button
            className={`tab-btn ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            📋 History
          </button>
          <button
            className={`tab-btn ${activeTab === 'preferences' ? 'active' : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            ⚙️ Preferences
          </button>
        </div>

        {/* Tab Content */}
        <div className="customer-detail-content">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="profile-tab">
              <div className="profile-section">
                <h3>Contact Information</h3>
                <div className="profile-grid">
                  <div className="profile-item">
                    <label>Email:</label>
                    <span>{customer.email}</span>
                  </div>
                  <div className="profile-item">
                    <label>Phone:</label>
                    <span>{customer.phone}</span>
                  </div>
                  <div className="profile-item">
                    <label>Date of Birth:</label>
                    <span>{formatDate(customer.dateOfBirth)} ({calculateAge(customer.dateOfBirth)})</span>
                  </div>
                </div>
              </div>

              <div className="profile-section">
                <h3>Address</h3>
                <div className="address-info">
                  {customer.address.street && (
                    <div>{customer.address.street}</div>
                  )}
                  {(customer.address.city || customer.address.state || customer.address.zipCode) && (
                    <div>
                      {customer.address.city && customer.address.city}
                      {customer.address.city && customer.address.state && ', '}
                      {customer.address.state && customer.address.state}
                      {customer.address.zipCode && ` ${customer.address.zipCode}`}
                    </div>
                  )}
                  {!customer.address.street && !customer.address.city && !customer.address.state && !customer.address.zipCode && (
                    <span className="no-data">No address provided</span>
                  )}
                </div>
              </div>

              <div className="profile-section">
                <h3>Account Information</h3>
                <div className="profile-grid">
                  <div className="profile-item">
                    <label>Customer Since:</label>
                    <span>{formatDate(customer.createdAt)}</span>
                  </div>
                  <div className="profile-item">
                    <label>Last Updated:</label>
                    <span>{formatDateTime(customer.updatedAt)}</span>
                  </div>
                  <div className="profile-item">
                    <label>Status:</label>
                    <span className={getStatusBadgeClass(customer.status)}>
                      {customer.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* History Tab */}
          {activeTab === 'history' && (
            <div className="history-tab">
              <div className="history-header">
                <h3>Appointment History</h3>
                <span className="history-count">
                  {customer.history.length} appointment{customer.history.length !== 1 ? 's' : ''}
                </span>
              </div>

              {customer.history.length === 0 ? (
                <div className="no-history">
                  <div className="no-history-icon">📅</div>
                  <p>No appointment history available</p>
                </div>
              ) : (
                <div className="history-list">
                  {customer.history.map((appointment, index) => (
                    <div key={appointment.id} className="history-item">
                      <div className="history-date">
                        {formatDateTime(appointment.date)}
                      </div>
                      <div className="history-details">
                        <div className="history-service">
                          <strong>{appointment.service}</strong>
                        </div>
                        <div className="history-staff">
                          Staff: {appointment.staff}
                        </div>
                        <div className="history-amount">
                          {formatCurrency(appointment.amount)}
                        </div>
                        {appointment.notes && (
                          <div className="history-notes">
                            <em>"{appointment.notes}"</em>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Preferences Tab */}
          {activeTab === 'preferences' && (
            <div className="preferences-tab">
              <div className="preferences-section">
                <h3>Preferred Services</h3>
                {customer.preferences.preferredServices.length > 0 ? (
                  <div className="preferences-tags">
                    {customer.preferences.preferredServices.map((service, index) => (
                      <span key={index} className="preference-tag">
                        {service}
                      </span>
                    ))}
                  </div>
                ) : (
                  <span className="no-data">No preferred services specified</span>
                )}
              </div>

              <div className="preferences-section">
                <h3>Preferred Staff</h3>
                {customer.preferences.preferredStaff.length > 0 ? (
                  <div className="preferences-tags">
                    {customer.preferences.preferredStaff.map((staff, index) => (
                      <span key={index} className="preference-tag">
                        {staff}
                      </span>
                    ))}
                  </div>
                ) : (
                  <span className="no-data">No preferred staff specified</span>
                )}
              </div>

              <div className="preferences-section">
                <h3>Allergies</h3>
                <div className="preferences-text">
                  {customer.preferences.allergies || (
                    <span className="no-data">No allergies specified</span>
                  )}
                </div>
              </div>

              <div className="preferences-section">
                <h3>Notes</h3>
                <div className="preferences-text">
                  {customer.preferences.notes || (
                    <span className="no-data">No notes available</span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerDetail;
