/* Modern Sign In Hero Section */
.signin-hero-section {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-top: 80px;
}

.signin-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-accent-600) 100%);
  z-index: 1;
}

.signin-hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="signin-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23signin-pattern)"/></svg>');
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.signin-hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  animation: pulse 4s ease-in-out infinite alternate;
}

.signin-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%);
  z-index: 2;
}

.signin-hero-content {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.signin-hero-text {
  max-width: 600px;
  color: white;
  text-align: center;
}

.signin-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out;
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.signin-hero-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.2);
}

.signin-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.8s ease-out 0.2s both;
  background: linear-gradient(135deg, #ffffff, var(--color-accent-200));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.signin-hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent-400), var(--color-primary-400));
  border-radius: 2px;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.signin-hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  opacity: 0.9;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Sign In Form Section */
.signin-form-section {
  position: relative;
  padding: 4rem 2rem;
  background: var(--color-gray-50);
}

.signin-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.signin-card {
  background: white;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  padding: 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid var(--color-gray-200);
  animation: slideInUp 0.8s ease-out 0.6s both;
  transition: all 0.3s ease;
}

.signin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
}

.signin-content {
  padding: 3rem;
}

.signin-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-accent-500));
  z-index: 1;
}



/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: var(--color-error-700);
}

.error-icon {
  width: 20px;
  height: 20px;
  color: var(--color-error-500);
  flex-shrink: 0;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Form Styles */
.signin-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  text-align: left;
}

.form-label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: 0.75rem;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.signin-input {
  width: 100%;
  padding: 1.25rem;
  border: 2px solid var(--color-gray-200);
  border-radius: 12px;
  font-size: var(--font-size-base);
  background: white;
  transition: all 0.3s ease;
  font-family: inherit;
}

.signin-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.signin-input:disabled {
  background: var(--color-gray-100);
  cursor: not-allowed;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  color: var(--color-gray-400);
  transition: color 0.3s ease;
  z-index: 2;
}

.password-toggle:hover:not(:disabled) {
  color: var(--color-gray-600);
}

.password-toggle:disabled {
  cursor: not-allowed;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 4px;
  background: white;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-text {
  font-weight: 500;
}

.forgot-link {
  color: var(--color-primary-600);
  text-decoration: none;
  font-size: var(--font-size-base);
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-link:hover {
  color: var(--color-primary-700);
}

/* Sign In Button */
.signin-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.signin-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.signin-btn:hover::before {
  left: 100%;
}

.signin-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.signin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.signin-footer {
  border-top: 1px solid var(--color-gray-200);
  padding-top: 1rem;
  margin-top: 1rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.footer-link {
  color: var(--color-gray-600);
  text-decoration: none;
  font-size: var(--font-size-xs);
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--color-primary-600);
}

.link-separator {
  color: var(--color-gray-400);
  font-size: var(--font-size-xs);
}

.copyright {
  color: var(--color-gray-500);
  font-size: var(--font-size-xs);
  text-align: center;
}
/* Responsive Design */
@media (max-width: 768px) {
  .signin-hero-section {
    height: 60vh;
  }

  .signin-hero-content {
    padding: 1.5rem;
  }

  .signin-hero-title {
    font-size: 2.5rem;
  }

  .signin-form-section {
    padding: 2rem 1.5rem;
  }

  .signin-content {
    padding: 2rem;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .footer-links {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .signin-form {
    gap: 0.875rem;
  }
}

@media (max-width: 480px) {
  .signin-hero-section {
    height: 50vh;
  }

  .signin-hero-content {
    padding: 1rem;
  }

  .signin-hero-title {
    font-size: 2rem;
  }

  .signin-form-section {
    padding: 1.5rem 1rem;
  }

  .signin-content {
    padding: 1.5rem;
  }

  .signin-input {
    padding: 0.75rem;
  }

  .password-toggle {
    right: 0.75rem;
  }

  .password-toggle svg {
    width: 16px;
    height: 16px;
  }

  .footer-links {
    flex-direction: column;
    gap: 0.375rem;
  }

  .link-separator {
    display: none;
  }

  .signin-form {
    gap: 0.75rem;
  }
}

@media (max-width: 360px) {
  .signin-hero-section {
    height: 45vh;
  }

  .signin-hero-content {
    padding: 0.75rem;
  }

  .signin-hero-title {
    font-size: 1.75rem;
  }

  .signin-form-section {
    padding: 1rem 0.75rem;
  }

  .signin-content {
    padding: 1rem;
  }
}

/* Keyframe Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}