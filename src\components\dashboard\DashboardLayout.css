/* Dashboard Layout Styles */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  position: relative;
}

.dashboard-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(217, 70, 239, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Enhanced Sidebar Styles */
.sidebar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--color-gray-800);
  transition: all var(--transition-base);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-fixed);
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.sidebar-open {
  width: var(--sidebar-width);
}

.sidebar-closed {
  width: var(--sidebar-width-collapsed);
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-base);
}

.logo-icon {
  font-size: var(--font-size-2xl);
  min-width: 32px;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  white-space: nowrap;
  background: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-nav {
  padding: var(--space-6) 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-4) var(--space-6);
  margin: var(--space-1) var(--space-3);
  background: none;
  border: none;
  border-radius: var(--radius-xl);
  color: var(--color-gray-600);
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-gray-800);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item-active {
  background: var(--gradient-primary);
  color: white;
  transform: translateX(4px);
  box-shadow: var(--shadow-lg);
}

.nav-item-active::before {
  display: none;
}

.nav-icon {
  font-size: var(--font-size-lg);
  min-width: 24px;
  text-align: center;
  transition: transform var(--transition-base);
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  white-space: nowrap;
  font-weight: var(--font-weight-medium);
}

/* Enhanced Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: all var(--transition-base);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.sidebar-closed + .main-content {
  margin-left: var(--sidebar-width-collapsed);
}

/* Enhanced Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
  height: var(--header-height);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  color: var(--color-gray-600);
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-gray-800);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin: 0;
  background: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* Enhanced User Menu */
.user-menu {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.user-menu-trigger:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-md);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-base);
}

.user-menu-trigger:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.8rem;
  color: #6c757d;
}

.dropdown-arrow {
  font-size: 0.7rem;
  color: #6c757d;
  transition: transform 0.2s ease;
}

.user-menu-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* User Menu Dropdown */
.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  z-index: 1000;
  overflow: hidden;
}

.user-menu-header {
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #3498db;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.user-role-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  color: white;
  font-weight: 500;
}

.user-menu-items {
  padding: 0.5rem 0;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
  color: #495057;
}

.user-menu-item:hover {
  background: #f8f9fa;
}

.menu-item-icon {
  font-size: 1rem;
}

/* Page Content */
.page-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .sidebar-overlay {
    display: block;
  }
  
  .user-info {
    display: none;
  }
  
  .page-title {
    font-size: 1.2rem;
  }
  
  .page-content {
    padding: 1rem;
  }
}
