import React, { useState, useEffect } from 'react';
import AppointmentCalendar from './AppointmentCalendar';
import AppointmentList from './AppointmentList';
import AppointmentForm from './AppointmentForm';
import AppointmentDetail from './AppointmentDetail';
import AppointmentReminders from './AppointmentReminders';
import appointmentService from '../../services/appointmentService';
import customerService from '../../services/customerService';
import toast from 'react-hot-toast';
import './AppointmentManagement.css';

const AppointmentManagement = () => {
  const [currentView, setCurrentView] = useState('calendar'); // 'calendar', 'list', 'form', 'detail', 'reminders'
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, [refreshTrigger]);

  const loadStats = async () => {
    try {
      const appointmentStats = appointmentService.getAppointmentStats();
      setStats(appointmentStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewChange = (view, data = null) => {
    setCurrentView(view);
    if (data) {
      if (view === 'detail') {
        setSelectedAppointment(data);
      } else if (view === 'form' && data.appointment) {
        setSelectedAppointment(data.appointment);
      }
    }
  };

  const handleAppointmentSave = (appointment) => {
    setRefreshTrigger(prev => prev + 1);
    setCurrentView('calendar');
    setSelectedAppointment(null);
    toast.success('Appointment saved successfully!');
  };

  const handleAppointmentSelect = (appointment) => {
    setSelectedAppointment(appointment);
    setCurrentView('detail');
  };

  const handleEditAppointment = (appointment) => {
    setSelectedAppointment(appointment);
    setCurrentView('form');
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    // Optionally switch to form view for quick booking
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleNewAppointment = () => {
    setSelectedAppointment(null);
    setCurrentView('form');
  };

  const renderHeader = () => (
    <div className="appointment-management-header">
      <div className="header-content">
        <h1>Appointment Management</h1>
        <p>Manage your salon's appointment schedule</p>
      </div>
      
      <div className="header-stats">
        <div className="stat-card">
          <div className="stat-number">{stats.today || 0}</div>
          <div className="stat-label">Today</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.thisWeek || 0}</div>
          <div className="stat-label">This Week</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.scheduled || 0}</div>
          <div className="stat-label">Scheduled</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{stats.completed || 0}</div>
          <div className="stat-label">Completed</div>
        </div>
      </div>

      <div className="header-actions">
        <button
          onClick={handleNewAppointment}
          className="btn btn-primary new-appointment-btn"
        >
          <span className="btn-icon">📅</span>
          <span className="btn-text">New Appointment</span>
        </button>
      </div>
    </div>
  );

  const renderNavigation = () => (
    <div className="appointment-navigation">
      <div className="nav-container">
        <button
          onClick={() => setCurrentView('calendar')}
          className={`nav-btn ${currentView === 'calendar' ? 'active' : ''}`}
        >
          <span className="nav-icon">📅</span>
          <span className="nav-text">Calendar</span>
        </button>
        <button
          onClick={() => setCurrentView('list')}
          className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}
        >
          <span className="nav-icon">📋</span>
          <span className="nav-text">List View</span>
        </button>
        <button
          onClick={() => setCurrentView('reminders')}
          className={`nav-btn ${currentView === 'reminders' ? 'active' : ''}`}
        >
          <span className="nav-icon">⏰</span>
          <span className="nav-text">Reminders</span>
        </button>
      </div>
      <div className="nav-indicator"></div>
    </div>
  );

  const renderContent = () => {
    switch (currentView) {
      case 'calendar':
        return (
          <AppointmentCalendar
            onDateSelect={handleDateSelect}
            onAppointmentSelect={handleAppointmentSelect}
            selectedDate={selectedDate}
          />
        );
      
      case 'list':
        return (
          <AppointmentList
            onAppointmentSelect={handleAppointmentSelect}
            onEditAppointment={handleEditAppointment}
            refreshTrigger={refreshTrigger}
          />
        );
      
      case 'form':
        return (
          <AppointmentForm
            appointment={selectedAppointment}
            preselectedDate={selectedDate}
            onSave={handleAppointmentSave}
            onCancel={() => {
              setCurrentView('calendar');
              setSelectedAppointment(null);
            }}
          />
        );
      
      case 'detail':
        return (
          <AppointmentDetail
            appointment={selectedAppointment}
            onClose={() => {
              setCurrentView('calendar');
              setSelectedAppointment(null);
            }}
            onEdit={handleEditAppointment}
            onRefresh={handleRefresh}
          />
        );
      
      case 'reminders':
        return (
          <AppointmentReminders
            onAppointmentSelect={handleAppointmentSelect}
          />
        );
      
      default:
        return (
          <div className="view-not-found">
            <p>View not found</p>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="appointment-management">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading appointment management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-management">
      {renderHeader()}
      {currentView !== 'form' && currentView !== 'detail' && renderNavigation()}
      <div className="appointment-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default AppointmentManagement;
