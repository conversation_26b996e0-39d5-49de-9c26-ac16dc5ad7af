import React, { useState } from 'react';
import appointmentService, { APPOINTMENT_STATUS } from '../../services/appointmentService';
import toast from 'react-hot-toast';
import './AppointmentDetail.css';

const AppointmentDetail = ({ appointment, onClose, onEdit, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [notes, setNotes] = useState(appointment?.notes || '');
  const [editingNotes, setEditingNotes] = useState(false);

  if (!appointment) {
    return null;
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getEndTime = (startTime, duration) => {
    const start = new Date(startTime);
    const end = new Date(start.getTime() + duration * 60000);
    return formatTime(end.toISOString());
  };

  const getStatusColor = (status) => {
    switch (status) {
      case APPOINTMENT_STATUS.SCHEDULED:
        return '#3498db';
      case APPOINTMENT_STATUS.CONFIRMED:
        return '#2ecc71';
      case APPOINTMENT_STATUS.IN_PROGRESS:
        return '#f39c12';
      case APPOINTMENT_STATUS.COMPLETED:
        return '#27ae60';
      case APPOINTMENT_STATUS.CANCELLED:
        return '#e74c3c';
      case APPOINTMENT_STATUS.NO_SHOW:
        return '#95a5a6';
      default:
        return '#bdc3c7';
    }
  };

  const getStatusLabel = (status) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const handleStatusChange = async (newStatus) => {
    setLoading(true);
    try {
      await appointmentService.updateAppointment(appointment.id, { status: newStatus });
      toast.success('Appointment status updated');
      onRefresh && onRefresh();
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast.error('Failed to update appointment status');
    } finally {
      setLoading(false);
    }
  };

  const handleNotesUpdate = async () => {
    setLoading(true);
    try {
      await appointmentService.updateAppointment(appointment.id, { notes });
      toast.success('Notes updated');
      setEditingNotes(false);
      onRefresh && onRefresh();
    } catch (error) {
      console.error('Error updating notes:', error);
      toast.error('Failed to update notes');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAppointment = async () => {
    if (!window.confirm('Are you sure you want to delete this appointment? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    try {
      await appointmentService.deleteAppointment(appointment.id);
      toast.success('Appointment deleted successfully');
      onClose && onClose();
      onRefresh && onRefresh();
    } catch (error) {
      console.error('Error deleting appointment:', error);
      toast.error('Failed to delete appointment');
    } finally {
      setLoading(false);
    }
  };

  const canChangeStatus = () => {
    const appointmentDate = new Date(appointment.date);
    const now = new Date();
    
    // Can't change status of past appointments to scheduled/confirmed
    if (appointmentDate < now && 
        [APPOINTMENT_STATUS.SCHEDULED, APPOINTMENT_STATUS.CONFIRMED].includes(appointment.status)) {
      return false;
    }
    
    return true;
  };

  const getAvailableStatusOptions = () => {
    const appointmentDate = new Date(appointment.date);
    const now = new Date();
    const isToday = appointmentDate.toDateString() === now.toDateString();
    const isPast = appointmentDate < now;
    
    let options = [];
    
    if (!isPast) {
      options.push(APPOINTMENT_STATUS.SCHEDULED);
      options.push(APPOINTMENT_STATUS.CONFIRMED);
    }
    
    if (isToday || isPast) {
      options.push(APPOINTMENT_STATUS.IN_PROGRESS);
      options.push(APPOINTMENT_STATUS.COMPLETED);
      options.push(APPOINTMENT_STATUS.NO_SHOW);
    }
    
    options.push(APPOINTMENT_STATUS.CANCELLED);
    
    return options;
  };

  const isAppointmentToday = () => {
    const appointmentDate = new Date(appointment.date);
    const today = new Date();
    return appointmentDate.toDateString() === today.toDateString();
  };

  const isAppointmentUpcoming = () => {
    const appointmentDate = new Date(appointment.date);
    const now = new Date();
    return appointmentDate > now;
  };

  return (
    <div className="appointment-detail">
      <div className="detail-header">
        <div className="header-content">
          <h2>Appointment Details</h2>
          <div className="appointment-id">ID: {appointment.id.slice(-8)}</div>
        </div>
        <button onClick={onClose} className="close-btn">✕</button>
      </div>

      <div className="detail-content">
        <div className="detail-section">
          <h3>Date & Time</h3>
          <div className="datetime-info">
            <div className="date-display">
              <span className="date-icon">📅</span>
              <div>
                <div className="date">{formatDate(appointment.date)}</div>
                <div className="time-range">
                  {formatTime(appointment.date)} - {getEndTime(appointment.date, appointment.duration)}
                </div>
                <div className="duration">Duration: {appointment.duration} minutes</div>
              </div>
            </div>
          </div>
        </div>

        <div className="detail-section">
          <h3>Customer Information</h3>
          <div className="customer-info">
            <div className="info-row">
              <span className="label">Name:</span>
              <span className="value">{appointment.customerName}</span>
            </div>
            {appointment.customerPhone && (
              <div className="info-row">
                <span className="label">Phone:</span>
                <span className="value">
                  <a href={`tel:${appointment.customerPhone}`}>{appointment.customerPhone}</a>
                </span>
              </div>
            )}
            {appointment.customerEmail && (
              <div className="info-row">
                <span className="label">Email:</span>
                <span className="value">
                  <a href={`mailto:${appointment.customerEmail}`}>{appointment.customerEmail}</a>
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="detail-section">
          <h3>Service & Staff</h3>
          <div className="service-info">
            <div className="info-row">
              <span className="label">Service:</span>
              <span className="value">{appointment.serviceName}</span>
            </div>
            <div className="info-row">
              <span className="label">Staff Member:</span>
              <span className="value">{appointment.staffName}</span>
            </div>
            <div className="info-row">
              <span className="label">Price:</span>
              <span className="value price">${appointment.price}</span>
            </div>
          </div>
        </div>

        <div className="detail-section">
          <h3>Status</h3>
          <div className="status-section">
            <div className="current-status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(appointment.status) }}
              >
                {getStatusLabel(appointment.status)}
              </span>
            </div>
            
            {canChangeStatus() && (
              <div className="status-actions">
                <label htmlFor="status-select">Change Status:</label>
                <select
                  id="status-select"
                  value={appointment.status}
                  onChange={(e) => handleStatusChange(e.target.value)}
                  disabled={loading}
                  className="status-select"
                >
                  {getAvailableStatusOptions().map(status => (
                    <option key={status} value={status}>
                      {getStatusLabel(status)}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </div>

        <div className="detail-section">
          <h3>Notes</h3>
          <div className="notes-section">
            {editingNotes ? (
              <div className="notes-edit">
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add notes about this appointment..."
                  rows="4"
                  className="notes-textarea"
                />
                <div className="notes-actions">
                  <button
                    onClick={handleNotesUpdate}
                    disabled={loading}
                    className="btn btn-primary btn-sm"
                  >
                    Save Notes
                  </button>
                  <button
                    onClick={() => {
                      setEditingNotes(false);
                      setNotes(appointment.notes || '');
                    }}
                    className="btn btn-secondary btn-sm"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="notes-display">
                <p className="notes-text">
                  {appointment.notes || 'No notes added for this appointment.'}
                </p>
                <button
                  onClick={() => setEditingNotes(true)}
                  className="btn btn-secondary btn-sm"
                >
                  Edit Notes
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="detail-section">
          <h3>Appointment History</h3>
          <div className="history-info">
            <div className="info-row">
              <span className="label">Created:</span>
              <span className="value">
                {new Date(appointment.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit'
                })}
              </span>
            </div>
            <div className="info-row">
              <span className="label">Last Updated:</span>
              <span className="value">
                {new Date(appointment.updatedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="detail-actions">
        <div className="action-group">
          {isAppointmentUpcoming() && (
            <button
              onClick={() => onEdit && onEdit(appointment)}
              className="btn btn-primary"
              disabled={loading}
            >
              Edit Appointment
            </button>
          )}
          
          {isAppointmentToday() && appointment.status === APPOINTMENT_STATUS.SCHEDULED && (
            <button
              onClick={() => handleStatusChange(APPOINTMENT_STATUS.CONFIRMED)}
              className="btn btn-success"
              disabled={loading}
            >
              Confirm Appointment
            </button>
          )}
          
          {isAppointmentToday() && appointment.status === APPOINTMENT_STATUS.CONFIRMED && (
            <button
              onClick={() => handleStatusChange(APPOINTMENT_STATUS.IN_PROGRESS)}
              className="btn btn-warning"
              disabled={loading}
            >
              Start Service
            </button>
          )}
          
          {appointment.status === APPOINTMENT_STATUS.IN_PROGRESS && (
            <button
              onClick={() => handleStatusChange(APPOINTMENT_STATUS.COMPLETED)}
              className="btn btn-success"
              disabled={loading}
            >
              Complete Service
            </button>
          )}
        </div>

        <div className="danger-actions">
          <button
            onClick={handleDeleteAppointment}
            className="btn btn-danger"
            disabled={loading}
          >
            Delete Appointment
          </button>
        </div>
      </div>
    </div>
  );
};

export default AppointmentDetail;
