import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';
import './UserProfile.css';

const UserProfile = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: '',
    address: '',
    bio: ''
  });

  const userDisplayInfo = authService.getUserDisplayInfo();
  const permissions = authService.getUserPermissions();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = () => {
    // In a real app, this would make an API call to update user data
    console.log('Saving user data:', formData);
    setIsEditing(false);
    // Show success message
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      phone: '',
      address: '',
      bio: ''
    });
    setIsEditing(false);
  };

  const tabs = [
    { id: 'profile', label: 'Profile Information', icon: '👤' },
    { id: 'permissions', label: 'Permissions', icon: '🔐' },
    { id: 'security', label: 'Security', icon: '🛡️' }
  ];

  return (
    <div className="user-profile">
      <div className="profile-header">
        <div className="profile-avatar-section">
          <div className="profile-avatar-large">
            {userDisplayInfo?.initials || 'U'}
          </div>
          <div className="profile-info">
            <h1 className="profile-name">{userDisplayInfo?.name}</h1>
            <p className="profile-email">{userDisplayInfo?.email}</p>
            <div className="profile-role-badge" style={{ backgroundColor: authService.getRoleColor(user?.role) }}>
              {authService.formatRole(user?.role)}
            </div>
          </div>
        </div>
        <div className="profile-actions">
          {!isEditing ? (
            <button className="btn-primary" onClick={() => setIsEditing(true)}>
              Edit Profile
            </button>
          ) : (
            <div className="edit-actions">
              <button className="btn-secondary" onClick={handleCancel}>
                Cancel
              </button>
              <button className="btn-primary" onClick={handleSave}>
                Save Changes
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="profile-content">
        <div className="profile-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'tab-active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        <div className="tab-content">
          {activeTab === 'profile' && (
            <div className="profile-form">
              <h2>Personal Information</h2>
              <div className="form-grid">
                <div className="form-group">
                  <label className="form-label">Full Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Email Address</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Phone Number</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="form-input"
                    placeholder="Enter phone number"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Address</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="form-input"
                    placeholder="Enter address"
                  />
                </div>
                <div className="form-group form-group-full">
                  <label className="form-label">Bio</label>
                  <textarea
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="form-textarea"
                    placeholder="Tell us about yourself"
                    rows="4"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="permissions-tab">
              <h2>Your Permissions</h2>
              <p className="permissions-description">
                Based on your role as <strong>{authService.formatRole(user?.role)}</strong>, 
                you have access to the following features:
              </p>
              <div className="permissions-list">
                {permissions.map((permission, index) => (
                  <div key={index} className="permission-item">
                    <span className="permission-icon">✅</span>
                    <span className="permission-name">
                      {permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="security-tab">
              <h2>Security Settings</h2>
              <div className="security-section">
                <h3>Password</h3>
                <p className="security-description">
                  Keep your account secure by using a strong password.
                </p>
                <button className="btn-secondary">Change Password</button>
              </div>
              <div className="security-section">
                <h3>Two-Factor Authentication</h3>
                <p className="security-description">
                  Add an extra layer of security to your account.
                </p>
                <button className="btn-secondary">Enable 2FA</button>
              </div>
              <div className="security-section">
                <h3>Login Sessions</h3>
                <p className="security-description">
                  Manage your active login sessions across devices.
                </p>
                <button className="btn-secondary">View Sessions</button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
