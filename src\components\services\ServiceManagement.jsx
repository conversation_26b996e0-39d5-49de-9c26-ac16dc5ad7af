import React, { useState, useEffect } from 'react';
import ServiceCatalog from './ServiceCatalog';
import ServiceForm from './ServiceForm';
import PackageManagement from './PackageManagement';
import serviceService from '../../services/serviceService';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';
import './ServiceManagement.css';

const ServiceManagement = () => {
  const [currentView, setCurrentView] = useState('catalog'); // 'catalog', 'form', 'packages'
  const [selectedService, setSelectedService] = useState(null);
  const [services, setServices] = useState([]);
  const [packages, setPackages] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { user, isAdmin, isStaffOrAdmin } = useAuth();

  useEffect(() => {
    loadData();
  }, [refreshTrigger]);

  const loadData = async () => {
    try {
      setLoading(true);
      const servicesData = serviceService.getAllServices();
      const packagesData = serviceService.getAllPackages();
      const statsData = serviceService.getServiceStats();
      
      setServices(servicesData);
      setPackages(packagesData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading service data:', error);
      toast.error('Failed to load service data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleServiceSave = async (serviceData) => {
    try {
      if (selectedService) {
        await serviceService.updateService(selectedService.id, serviceData);
        toast.success('Service updated successfully');
      } else {
        await serviceService.addService(serviceData);
        toast.success('Service created successfully');
      }
      
      setCurrentView('catalog');
      setSelectedService(null);
      handleRefresh();
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error('Failed to save service');
    }
  };

  const handleServiceEdit = (service) => {
    setSelectedService(service);
    setCurrentView('form');
  };

  const handleServiceDelete = async (serviceId) => {
    if (!window.confirm('Are you sure you want to delete this service?')) {
      return;
    }

    try {
      await serviceService.deleteService(serviceId);
      toast.success('Service deleted successfully');
      handleRefresh();
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  const handleAddService = () => {
    setSelectedService(null);
    setCurrentView('form');
  };

  const handleBackToCatalog = () => {
    setCurrentView('catalog');
    setSelectedService(null);
  };

  if (loading) {
    return (
      <div className="service-management">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading services...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="service-management">
      {/* Header */}
      <div className="service-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="page-title">
              <span className="title-icon">🛍️</span>
              Service Management
            </h1>
            <p className="page-subtitle">
              Manage your salon services, pricing, and packages
            </p>
          </div>

          <div className="header-actions">
            <button
              className="btn btn-secondary"
              onClick={handleRefresh}
            >
              <span className="btn-icon">🔄</span>
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">🛍️</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalServices}</div>
            <div className="stat-label">Total Services</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <div className="stat-value">{stats.activeServices}</div>
            <div className="stat-label">Active Services</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">🎁</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalPackages}</div>
            <div className="stat-label">Packages</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <div className="stat-value">${stats.avgPrice}</div>
            <div className="stat-label">Avg. Price</div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="service-nav">
        <button
          className={`nav-tab ${currentView === 'catalog' ? 'nav-tab-active' : ''}`}
          onClick={() => setCurrentView('catalog')}
        >
          <span className="tab-icon">📋</span>
          Service Catalog
        </button>

        {isStaffOrAdmin() && (
          <button
            className={`nav-tab ${currentView === 'packages' ? 'nav-tab-active' : ''}`}
            onClick={() => setCurrentView('packages')}
          >
            <span className="tab-icon">🎁</span>
            Packages
          </button>
        )}
      </div>

      {/* Main Content */}
      <div className="service-content">
        {currentView === 'catalog' && (
          <ServiceCatalog
            services={services}
            onEdit={handleServiceEdit}
            onDelete={handleServiceDelete}
            canEdit={isStaffOrAdmin()}
          />
        )}

        {currentView === 'form' && (
          <ServiceForm
            service={selectedService}
            onSave={handleServiceSave}
            onCancel={handleBackToCatalog}
          />
        )}

        {currentView === 'packages' && (
          <PackageManagement
            packages={packages}
            services={services}
            onRefresh={handleRefresh}
            canEdit={isStaffOrAdmin()}
          />
        )}
      </div>
    </div>
  );
};

export default ServiceManagement;
