// Schedule Service - Staff Schedule Management
import { generateId } from '../utils/helpers';

// Schedule Types
export const SCHEDULE_TYPES = {
  REGULAR: 'regular',
  OVERTIME: 'overtime',
  BREAK: 'break',
  TIME_OFF: 'time_off',
  SICK_LEAVE: 'sick_leave',
  VACATION: 'vacation'
};

// Days of the week
export const DAYS_OF_WEEK = {
  MONDAY: 'monday',
  TUESDAY: 'tuesday',
  WEDNESDAY: 'wednesday',
  THURSDAY: 'thursday',
  FRIDAY: 'friday',
  SATURDAY: 'saturday',
  SUNDAY: 'sunday'
};

// Time off status
export const TIME_OFF_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected'
};

// Mock data for staff schedules
const mockSchedules = {
  'staff-1': {
    id: 'staff-1',
    staffId: 'staff-1',
    regularSchedule: {
      monday: { isWorking: true, startTime: '09:00', endTime: '17:00', breakTime: '12:00-13:00' },
      tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00', breakTime: '12:00-13:00' },
      wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00', breakTime: '12:00-13:00' },
      thursday: { isWorking: true, startTime: '09:00', endTime: '17:00', breakTime: '12:00-13:00' },
      friday: { isWorking: true, startTime: '09:00', endTime: '17:00', breakTime: '12:00-13:00' },
      saturday: { isWorking: true, startTime: '10:00', endTime: '16:00', breakTime: '13:00-14:00' },
      sunday: { isWorking: false, startTime: '', endTime: '', breakTime: '' }
    },
    timeOffRequests: [
      {
        id: 'to-1',
        staffId: 'staff-1',
        type: TIME_OFF_STATUS.APPROVED,
        startDate: '2024-01-15',
        endDate: '2024-01-17',
        reason: 'Family vacation',
        status: TIME_OFF_STATUS.APPROVED,
        requestedDate: '2024-01-01',
        approvedBy: 'manager-1',
        approvedDate: '2024-01-02'
      },
      {
        id: 'to-2',
        staffId: 'staff-1',
        type: TIME_OFF_STATUS.PENDING,
        startDate: '2024-02-10',
        endDate: '2024-02-10',
        reason: 'Medical appointment',
        status: TIME_OFF_STATUS.PENDING,
        requestedDate: '2024-01-20'
      }
    ],
    scheduleExceptions: [
      {
        id: 'ex-1',
        staffId: 'staff-1',
        date: '2024-01-20',
        startTime: '10:00',
        endTime: '18:00',
        reason: 'Extended hours for special event',
        type: SCHEDULE_TYPES.OVERTIME
      }
    ]
  }
};

// Mock data for all staff schedules
let staffSchedules = { ...mockSchedules };

class ScheduleService {
  // Get staff schedule
  getStaffSchedule(staffId) {
    return staffSchedules[staffId] || {
      id: staffId,
      staffId,
      regularSchedule: {
        monday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        tuesday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        wednesday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        thursday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        friday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        saturday: { isWorking: false, startTime: '', endTime: '', breakTime: '' },
        sunday: { isWorking: false, startTime: '', endTime: '', breakTime: '' }
      },
      timeOffRequests: [],
      scheduleExceptions: []
    };
  }

  // Update regular schedule
  updateRegularSchedule(staffId, schedule) {
    if (!staffSchedules[staffId]) {
      staffSchedules[staffId] = this.getStaffSchedule(staffId);
    }
    
    staffSchedules[staffId].regularSchedule = { ...schedule };
    this.saveToStorage();
    return staffSchedules[staffId];
  }

  // Add time off request
  addTimeOffRequest(staffId, request) {
    if (!staffSchedules[staffId]) {
      staffSchedules[staffId] = this.getStaffSchedule(staffId);
    }

    const newRequest = {
      id: generateId(),
      staffId,
      ...request,
      requestedDate: new Date().toISOString().split('T')[0],
      status: TIME_OFF_STATUS.PENDING
    };

    staffSchedules[staffId].timeOffRequests.push(newRequest);
    this.saveToStorage();
    return newRequest;
  }

  // Update time off request status
  updateTimeOffStatus(staffId, requestId, status, approvedBy = null) {
    if (!staffSchedules[staffId]) return null;

    const request = staffSchedules[staffId].timeOffRequests.find(r => r.id === requestId);
    if (!request) return null;

    request.status = status;
    if (status === TIME_OFF_STATUS.APPROVED && approvedBy) {
      request.approvedBy = approvedBy;
      request.approvedDate = new Date().toISOString().split('T')[0];
    }

    this.saveToStorage();
    return request;
  }

  // Delete time off request
  deleteTimeOffRequest(staffId, requestId) {
    if (!staffSchedules[staffId]) return false;

    const index = staffSchedules[staffId].timeOffRequests.findIndex(r => r.id === requestId);
    if (index === -1) return false;

    staffSchedules[staffId].timeOffRequests.splice(index, 1);
    this.saveToStorage();
    return true;
  }

  // Add schedule exception
  addScheduleException(staffId, exception) {
    if (!staffSchedules[staffId]) {
      staffSchedules[staffId] = this.getStaffSchedule(staffId);
    }

    const newException = {
      id: generateId(),
      staffId,
      ...exception
    };

    staffSchedules[staffId].scheduleExceptions.push(newException);
    this.saveToStorage();
    return newException;
  }

  // Delete schedule exception
  deleteScheduleException(staffId, exceptionId) {
    if (!staffSchedules[staffId]) return false;

    const index = staffSchedules[staffId].scheduleExceptions.findIndex(e => e.id === exceptionId);
    if (index === -1) return false;

    staffSchedules[staffId].scheduleExceptions.splice(index, 1);
    this.saveToStorage();
    return true;
  }

  // Get schedule for date range
  getScheduleForDateRange(staffId, startDate, endDate) {
    const schedule = this.getStaffSchedule(staffId);
    const start = new Date(startDate);
    const end = new Date(endDate);
    const scheduleData = [];

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      
      // Check for exceptions first
      const exception = schedule.scheduleExceptions.find(e => e.date === dateStr);
      if (exception) {
        scheduleData.push({
          date: dateStr,
          dayName,
          isWorking: true,
          startTime: exception.startTime,
          endTime: exception.endTime,
          type: exception.type,
          reason: exception.reason,
          isException: true
        });
        continue;
      }

      // Check for time off
      const timeOff = schedule.timeOffRequests.find(r => 
        r.status === TIME_OFF_STATUS.APPROVED &&
        dateStr >= r.startDate && 
        dateStr <= r.endDate
      );
      
      if (timeOff) {
        scheduleData.push({
          date: dateStr,
          dayName,
          isWorking: false,
          type: SCHEDULE_TYPES.TIME_OFF,
          reason: timeOff.reason,
          isTimeOff: true
        });
        continue;
      }

      // Regular schedule
      const regularDay = schedule.regularSchedule[dayName];
      scheduleData.push({
        date: dateStr,
        dayName,
        isWorking: regularDay.isWorking,
        startTime: regularDay.startTime,
        endTime: regularDay.endTime,
        breakTime: regularDay.breakTime,
        type: SCHEDULE_TYPES.REGULAR
      });
    }

    return scheduleData;
  }

  // Calculate total hours for a period
  calculateHours(staffId, startDate, endDate) {
    const scheduleData = this.getScheduleForDateRange(staffId, startDate, endDate);
    let totalHours = 0;
    let regularHours = 0;
    let overtimeHours = 0;

    scheduleData.forEach(day => {
      if (day.isWorking && day.startTime && day.endTime) {
        const start = new Date(`2000-01-01T${day.startTime}`);
        const end = new Date(`2000-01-01T${day.endTime}`);
        const hours = (end - start) / (1000 * 60 * 60);
        
        totalHours += hours;
        
        if (day.type === SCHEDULE_TYPES.OVERTIME) {
          overtimeHours += hours;
        } else {
          regularHours += hours;
        }
      }
    });

    return {
      totalHours: Math.round(totalHours * 100) / 100,
      regularHours: Math.round(regularHours * 100) / 100,
      overtimeHours: Math.round(overtimeHours * 100) / 100
    };
  }

  // Save to localStorage
  saveToStorage() {
    try {
      localStorage.setItem('staffSchedules', JSON.stringify(staffSchedules));
    } catch (error) {
      console.error('Error saving schedules to localStorage:', error);
    }
  }

  // Load from localStorage
  loadFromStorage() {
    try {
      const stored = localStorage.getItem('staffSchedules');
      if (stored) {
        staffSchedules = { ...mockSchedules, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.error('Error loading schedules from localStorage:', error);
      staffSchedules = { ...mockSchedules };
    }
  }
}

// Initialize service
const scheduleService = new ScheduleService();
scheduleService.loadFromStorage();

export default scheduleService;
