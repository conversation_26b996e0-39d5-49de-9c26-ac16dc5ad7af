import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';
import { useNavigate } from 'react-router-dom';
import DashboardCharts from './DashboardCharts';
import './DashboardHome.css';

const DashboardHome = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const userDisplayInfo = authService.getUserDisplayInfo();
  const permissions = authService.getUserPermissions();

  // State for dashboard data
  const [dashboardStats, setDashboardStats] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [chartData, setChartData] = useState(null);
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [loading, setLoading] = useState(true);

  // Import dashboard service
  const [dashboardService, setDashboardService] = useState(null);

  useEffect(() => {
    const loadDashboardService = async () => {
      const { default: service } = await import('../../services/dashboardService');
      setDashboardService(service);
    };
    loadDashboardService();
  }, []);

  useEffect(() => {
    if (!dashboardService) return;

    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Load all dashboard data
        const [statsResponse, activitiesResponse, chartsResponse, appointmentsResponse] = await Promise.all([
          dashboardService.getDashboardStats(),
          dashboardService.getRecentActivities(),
          dashboardService.getChartData(),
          dashboardService.getUpcomingAppointments()
        ]);

        // Process stats based on user permissions
        const stats = [
          {
            title: 'Today\'s Appointments',
            value: statsResponse.data.todayAppointments.toString(),
            icon: '📅',
            color: '#3498db',
            change: '+2.5%',
            changeType: 'increase',
            visible: authService.canAccessAppointments()
          },
          {
            title: 'Total Customers',
            value: statsResponse.data.totalCustomers.toString(),
            icon: '👥',
            color: '#2ecc71',
            change: '+12.3%',
            changeType: 'increase',
            visible: authService.canAccessCustomers()
          },
          {
            title: 'Monthly Revenue',
            value: dashboardService.formatCurrency(statsResponse.data.monthlyRevenue),
            icon: '💰',
            color: '#f39c12',
            change: '+8.1%',
            changeType: 'increase',
            visible: authService.canAccessBilling()
          },
          {
            title: 'Staff Members',
            value: statsResponse.data.staffMembers.toString(),
            icon: '👨‍💼',
            color: '#9b59b6',
            change: '0%',
            changeType: 'neutral',
            visible: authService.canAccessStaff()
          },
          {
            title: 'Pending Appointments',
            value: statsResponse.data.pendingAppointments.toString(),
            icon: '⏰',
            color: '#e74c3c',
            change: '-5.2%',
            changeType: 'decrease',
            visible: authService.canAccessAppointments()
          },
          {
            title: 'Customer Satisfaction',
            value: `${statsResponse.data.customerSatisfaction}/5`,
            icon: '⭐',
            color: '#f1c40f',
            change: '+0.2',
            changeType: 'increase',
            visible: authService.canAccessReports()
          }
        ].filter(stat => stat.visible);

        setDashboardStats(stats);
        setRecentActivities(activitiesResponse.data);
        setChartData(chartsResponse.data);
        setUpcomingAppointments(appointmentsResponse.data);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();

    // Set up real-time updates
    const interval = setInterval(() => {
      dashboardService.updateStats();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardService]);

  const quickActions = [
    {
      title: 'New Appointment',
      description: 'Schedule a new appointment',
      icon: '➕',
      color: '#3498db',
      action: () => navigate('/dashboard/appointments'),
      visible: authService.canAccessAppointments()
    },
    {
      title: 'Add Customer',
      description: 'Register a new customer',
      icon: '👤',
      color: '#2ecc71',
      action: () => navigate('/dashboard/customers'),
      visible: authService.canAccessCustomers()
    },
    {
      title: 'View Reports',
      description: 'Check business analytics',
      icon: '📊',
      color: '#f39c12',
      action: () => navigate('/dashboard/reports'),
      visible: authService.canAccessReports()
    },
    {
      title: 'Manage Inventory',
      description: 'Update product stock',
      icon: '📦',
      color: '#9b59b6',
      action: () => navigate('/dashboard/inventory'),
      visible: authService.canAccessInventory()
    },
    {
      title: 'Manage Services',
      description: 'Add or edit services',
      icon: '✂️',
      color: '#e74c3c',
      action: () => navigate('/dashboard/services'),
      visible: authService.canAccessServices()
    },
    {
      title: 'Staff Management',
      description: 'Manage staff members',
      icon: '👨‍💼',
      color: '#34495e',
      action: () => navigate('/dashboard/staff'),
      visible: authService.canAccessStaff()
    }
  ].filter(action => action.visible);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getChangeIcon = (changeType) => {
    switch (changeType) {
      case 'increase': return '📈';
      case 'decrease': return '📉';
      default: return '➖';
    }
  };

  const getChangeColor = (changeType) => {
    switch (changeType) {
      case 'increase': return '#2ecc71';
      case 'decrease': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  if (loading) {
    return (
      <div className="dashboard-home">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-home">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-content">
          <h1 className="welcome-title">
            {getGreeting()}, {userDisplayInfo?.name}! 👋
          </h1>
          <p className="welcome-subtitle">
            Welcome to your salon management dashboard. Here's what's happening today.
          </p>
        </div>
        <div className="user-role-info">
          <div className="role-badge" style={{ backgroundColor: authService.getRoleColor(user?.role) }}>
            {authService.formatRole(user?.role)}
          </div>
        </div>
      </div>

      {/* Dashboard Stats */}
      {dashboardStats.length > 0 && (
        <div className="stats-section">
          <h2 className="section-title">Overview</h2>
          <div className="stats-grid">
            {dashboardStats.map((stat, index) => (
              <div key={index} className="stat-card">
                <div className="stat-icon" style={{ backgroundColor: stat.color }}>
                  {stat.icon}
                </div>
                <div className="stat-content">
                  <h3 className="stat-value">{stat.value}</h3>
                  <p className="stat-title">{stat.title}</p>
                  {stat.change && (
                    <div className="stat-change" style={{ color: getChangeColor(stat.changeType) }}>
                      <span className="change-icon">{getChangeIcon(stat.changeType)}</span>
                      <span className="change-text">{stat.change}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {quickActions.length > 0 && (
        <div className="actions-section">
          <h2 className="section-title">Quick Actions</h2>
          <div className="actions-grid">
            {quickActions.map((action, index) => (
              <button
                key={index}
                className="action-card"
                onClick={action.action}
              >
                <div className="action-icon" style={{ backgroundColor: action.color }}>
                  {action.icon}
                </div>
                <div className="action-content">
                  <h3 className="action-title">{action.title}</h3>
                  <p className="action-description">{action.description}</p>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="main-content-grid">
        {/* Recent Activities */}
        <div className="content-card">
          <div className="card-header">
            <h2 className="section-title">Recent Activities</h2>
            <button className="view-all-btn" onClick={() => navigate('/dashboard/reports')}>
              View All
            </button>
          </div>
          <div className="activity-list">
            {recentActivities.length > 0 ? (
              recentActivities.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className="activity-icon">
                    {activity.icon}
                  </div>
                  <div className="activity-content">
                    <p className="activity-text">{activity.message}</p>
                    <span className="activity-time">{activity.time}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-state">
                <p>No recent activities</p>
              </div>
            )}
          </div>
        </div>

        {/* Upcoming Appointments */}
        <div className="content-card">
          <div className="card-header">
            <h2 className="section-title">Today's Appointments</h2>
            <button className="view-all-btn" onClick={() => navigate('/dashboard/appointments')}>
              View All
            </button>
          </div>
          <div className="appointments-list">
            {upcomingAppointments.length > 0 ? (
              upcomingAppointments.map((appointment) => (
                <div key={appointment.id} className="appointment-item">
                  <div className="appointment-time">
                    <span className="time">{appointment.time}</span>
                    <span className="duration">{appointment.duration}</span>
                  </div>
                  <div className="appointment-details">
                    <h4 className="customer-name">{appointment.customerName}</h4>
                    <p className="service-name">{appointment.service}</p>
                    <p className="staff-name">with {appointment.staff}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-state">
                <p>No appointments scheduled</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Charts Section */}
      {chartData && <DashboardCharts chartData={chartData} />}

      {/* User Permissions */}
      {permissions.length > 0 && (
        <div className="permissions-section">
          <h2 className="section-title">Your Permissions</h2>
          <div className="permissions-grid">
            {permissions.map((permission, index) => (
              <div key={index} className="permission-item">
                <span className="permission-icon">✅</span>
                <span className="permission-text">
                  {permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardHome;
