// Appointment Service for Salon Management System
import { v4 as uuidv4 } from 'uuid';

// Appointment Status Types
export const APPOINTMENT_STATUS = {
  SCHEDULED: 'scheduled',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show'
};

// Time Slot Configuration
export const TIME_SLOTS = {
  DURATION: 30, // minutes
  START_HOUR: 9, // 9 AM
  END_HOUR: 18, // 6 PM
  BREAK_START: 13, // 1 PM
  BREAK_END: 14, // 2 PM
  DAYS_ADVANCE: 30 // How many days in advance to allow booking
};

// Service Types with durations
export const SERVICES = [
  { id: 'haircut', name: 'Haircut', duration: 60, price: 50 },
  { id: 'hair_wash', name: 'Hair Wash & Blow Dry', duration: 45, price: 30 },
  { id: 'hair_color', name: 'Hair Coloring', duration: 120, price: 100 },
  { id: 'highlights', name: 'Highlights', duration: 150, price: 120 },
  { id: 'perm', name: 'Perm', duration: 180, price: 80 },
  { id: 'facial', name: 'Facial Treatment', duration: 90, price: 70 },
  { id: 'manicure', name: 'Manicure', duration: 45, price: 25 },
  { id: 'pedicure', name: 'Pedicure', duration: 60, price: 35 },
  { id: 'eyebrow', name: 'Eyebrow Threading', duration: 30, price: 15 },
  { id: 'massage', name: 'Head Massage', duration: 30, price: 20 }
];

// Staff Members
export const STAFF_MEMBERS = [
  { id: 'staff1', name: 'Sarah Johnson', specialties: ['haircut', 'hair_color', 'highlights'] },
  { id: 'staff2', name: 'Maria Garcia', specialties: ['facial', 'eyebrow', 'massage'] },
  { id: 'staff3', name: 'Lisa Chen', specialties: ['manicure', 'pedicure'] },
  { id: 'staff4', name: 'Emma Wilson', specialties: ['haircut', 'hair_wash', 'perm'] }
];

class AppointmentService {
  constructor() {
    this.storageKey = 'salon_appointments';
    this.appointments = this.loadAppointments();
  }

  // Load appointments from localStorage
  loadAppointments() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : this.generateDemoAppointments();
    } catch (error) {
      console.error('Error loading appointments:', error);
      return this.generateDemoAppointments();
    }
  }

  // Save appointments to localStorage
  saveAppointments() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.appointments));
    } catch (error) {
      console.error('Error saving appointments:', error);
    }
  }

  // Generate demo appointments
  generateDemoAppointments() {
    const demoAppointments = [];
    const today = new Date();
    
    // Generate appointments for the next 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Skip Sundays
      if (date.getDay() === 0) continue;
      
      // Add 2-3 random appointments per day
      const appointmentsPerDay = Math.floor(Math.random() * 2) + 2;
      
      for (let j = 0; j < appointmentsPerDay; j++) {
        const service = SERVICES[Math.floor(Math.random() * SERVICES.length)];
        const staff = STAFF_MEMBERS.find(s => s.specialties.includes(service.id)) || STAFF_MEMBERS[0];
        
        const hour = 9 + Math.floor(Math.random() * 8); // 9 AM to 5 PM
        const minute = Math.random() < 0.5 ? 0 : 30;
        
        const appointmentDate = new Date(date);
        appointmentDate.setHours(hour, minute, 0, 0);
        
        demoAppointments.push({
          id: uuidv4(),
          customerId: `customer${j + 1}`,
          customerName: `Customer ${j + 1}`,
          customerPhone: `555-010${j + 1}`,
          customerEmail: `customer${j + 1}@email.com`,
          serviceId: service.id,
          serviceName: service.name,
          staffId: staff.id,
          staffName: staff.name,
          date: appointmentDate.toISOString(),
          duration: service.duration,
          price: service.price,
          status: i === 0 && j === 0 ? APPOINTMENT_STATUS.IN_PROGRESS : 
                  Math.random() > 0.8 ? APPOINTMENT_STATUS.CANCELLED : APPOINTMENT_STATUS.SCHEDULED,
          notes: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
    }
    
    return demoAppointments;
  }

  // Get all appointments
  getAllAppointments() {
    return [...this.appointments];
  }

  // Get appointments by date range
  getAppointmentsByDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return this.appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return appointmentDate >= start && appointmentDate <= end;
    });
  }

  // Get appointments by date
  getAppointmentsByDate(date) {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(targetDate.getDate() + 1);
    
    return this.appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return appointmentDate >= targetDate && appointmentDate < nextDay;
    });
  }

  // Get appointment by ID
  getAppointmentById(id) {
    return this.appointments.find(appointment => appointment.id === id);
  }

  // Create new appointment
  createAppointment(appointmentData) {
    // Validate required fields
    const required = ['customerId', 'customerName', 'serviceId', 'staffId', 'date'];
    for (const field of required) {
      if (!appointmentData[field]) {
        throw new Error(`${field} is required`);
      }
    }

    const service = SERVICES.find(s => s.id === appointmentData.serviceId);
    const staff = STAFF_MEMBERS.find(s => s.id === appointmentData.staffId);

    // Get duration from service data
    const duration = appointmentData.duration || service?.duration || 60;

    // Check for conflicts
    if (this.hasTimeConflict(appointmentData.staffId, appointmentData.date, duration)) {
      throw new Error('Time slot is not available for the selected staff member');
    }

    const newAppointment = {
      id: uuidv4(),
      customerId: appointmentData.customerId,
      customerName: appointmentData.customerName,
      customerPhone: appointmentData.customerPhone || '',
      customerEmail: appointmentData.customerEmail || '',
      serviceId: appointmentData.serviceId,
      serviceName: service?.name || '',
      staffId: appointmentData.staffId,
      staffName: staff?.name || '',
      date: appointmentData.date,
      duration: duration,
      price: service?.price || 0,
      status: APPOINTMENT_STATUS.SCHEDULED,
      notes: appointmentData.notes || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.appointments.push(newAppointment);
    this.saveAppointments();
    return newAppointment;
  }

  // Update appointment
  updateAppointment(id, updates) {
    const index = this.appointments.findIndex(appointment => appointment.id === id);
    if (index === -1) {
      throw new Error('Appointment not found');
    }

    // If updating time/staff, check for conflicts
    if (updates.date || updates.staffId) {
      const appointment = this.appointments[index];
      const newDate = updates.date || appointment.date;
      const newStaffId = updates.staffId || appointment.staffId;
      const duration = appointment.duration;

      if (this.hasTimeConflict(newStaffId, newDate, duration, id)) {
        throw new Error('Time slot is not available for the selected staff member');
      }
    }

    this.appointments[index] = {
      ...this.appointments[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveAppointments();
    return this.appointments[index];
  }

  // Delete appointment
  deleteAppointment(id) {
    const index = this.appointments.findIndex(appointment => appointment.id === id);
    if (index === -1) {
      throw new Error('Appointment not found');
    }

    const deletedAppointment = this.appointments.splice(index, 1)[0];
    this.saveAppointments();
    return deletedAppointment;
  }

  // Check for time conflicts
  hasTimeConflict(staffId, date, duration, excludeId = null) {
    const appointmentStart = new Date(date);
    const appointmentEnd = new Date(appointmentStart.getTime() + duration * 60000);

    return this.appointments.some(appointment => {
      if (appointment.id === excludeId) return false;
      if (appointment.staffId !== staffId) return false;
      if (appointment.status === APPOINTMENT_STATUS.CANCELLED) return false;

      const existingStart = new Date(appointment.date);
      const existingEnd = new Date(existingStart.getTime() + appointment.duration * 60000);

      return (appointmentStart < existingEnd && appointmentEnd > existingStart);
    });
  }

  // Generate available time slots for a date and staff member
  getAvailableTimeSlots(date, staffId) {
    const targetDate = new Date(date);
    const slots = [];
    
    // Generate all possible slots
    for (let hour = TIME_SLOTS.START_HOUR; hour < TIME_SLOTS.END_HOUR; hour++) {
      // Skip lunch break
      if (hour >= TIME_SLOTS.BREAK_START && hour < TIME_SLOTS.BREAK_END) continue;
      
      for (let minute = 0; minute < 60; minute += TIME_SLOTS.DURATION) {
        const slotTime = new Date(targetDate);
        slotTime.setHours(hour, minute, 0, 0);
        
        // Don't show past time slots for today
        if (slotTime <= new Date()) continue;
        
        slots.push(slotTime);
      }
    }
    
    // Filter out conflicting slots
    return slots.filter(slot => {
      return !this.hasTimeConflict(staffId, slot.toISOString(), TIME_SLOTS.DURATION);
    });
  }

  // Get appointments statistics
  getAppointmentStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayAppointments = this.getAppointmentsByDate(today);
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay());
    const thisWeekEnd = new Date(thisWeekStart);
    thisWeekEnd.setDate(thisWeekStart.getDate() + 7);
    
    const thisWeekAppointments = this.getAppointmentsByDateRange(thisWeekStart, thisWeekEnd);
    
    return {
      total: this.appointments.length,
      today: todayAppointments.length,
      thisWeek: thisWeekAppointments.length,
      scheduled: this.appointments.filter(a => a.status === APPOINTMENT_STATUS.SCHEDULED).length,
      completed: this.appointments.filter(a => a.status === APPOINTMENT_STATUS.COMPLETED).length,
      cancelled: this.appointments.filter(a => a.status === APPOINTMENT_STATUS.CANCELLED).length
    };
  }

  // Get services
  getServices() {
    return [...SERVICES];
  }

  // Get staff members
  getStaffMembers() {
    return [...STAFF_MEMBERS];
  }

  // Get staff members who can perform a service
  getStaffForService(serviceId) {
    return STAFF_MEMBERS.filter(staff => staff.specialties.includes(serviceId));
  }
}

// Create and export singleton instance
const appointmentService = new AppointmentService();
export default appointmentService;
