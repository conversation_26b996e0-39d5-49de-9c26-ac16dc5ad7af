/* Loading Spinner Styles */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-out;
}

[data-theme="dark"] .loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.loading-spinner {
  position: relative;
  display: inline-block;
}

.spinner-ring {
  position: absolute;
  border-radius: 50%;
  animation: spinner-rotate 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-style: solid;
  border-color: transparent;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
}

/* Spinner Sizes */
.spinner-sm {
  width: 20px;
  height: 20px;
}

.spinner-sm .spinner-ring {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner-md {
  width: 32px;
  height: 32px;
}

.spinner-md .spinner-ring {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.spinner-lg {
  width: 48px;
  height: 48px;
}

.spinner-lg .spinner-ring {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

.spinner-xl {
  width: 64px;
  height: 64px;
}

.spinner-xl .spinner-ring {
  width: 64px;
  height: 64px;
  border-width: 5px;
}

/* Spinner Colors */
.spinner-primary .spinner-ring {
  border-top-color: var(--color-primary-500);
}

.spinner-secondary .spinner-ring {
  border-top-color: var(--color-secondary-500);
}

.spinner-success .spinner-ring {
  border-top-color: var(--color-success-500);
}

.spinner-warning .spinner-ring {
  border-top-color: var(--color-warning-500);
}

.spinner-error .spinner-ring {
  border-top-color: var(--color-error-500);
}

.spinner-white .spinner-ring {
  border-top-color: white;
}

.loading-text {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: 0;
  text-align: center;
}

/* Skeleton Loader */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

[data-theme="dark"] .skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-700) 25%,
    var(--color-gray-600) 50%,
    var(--color-gray-700) 75%
  );
  background-size: 200% 100%;
}

/* Pulse Loader */
.pulse-container {
  transition: opacity var(--transition-base);
}

.pulse-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Dots Loader */
.dots-loader {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  justify-content: center;
}

.dots-loader .dot {
  border-radius: 50%;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}

.dots-loader .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dots-loader .dot:nth-child(2) {
  animation-delay: -0.16s;
}

/* Dots Sizes */
.dots-sm .dot {
  width: 6px;
  height: 6px;
}

.dots-md .dot {
  width: 8px;
  height: 8px;
}

.dots-lg .dot {
  width: 12px;
  height: 12px;
}

/* Dots Colors */
.dots-primary .dot {
  background: var(--color-primary-500);
}

.dots-secondary .dot {
  background: var(--color-secondary-500);
}

.dots-white .dot {
  background: white;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
}

.progress-bar {
  flex: 1;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

[data-theme="dark"] .progress-bar {
  background: var(--color-gray-700);
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
  position: relative;
  overflow: hidden;
}

.progress-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progress-shimmer 2s infinite;
}

/* Progress Sizes */
.progress-sm {
  height: 4px;
}

.progress-md {
  height: 8px;
}

.progress-lg {
  height: 12px;
}

/* Progress Colors */
.progress-primary {
  background: var(--gradient-primary);
}

.progress-secondary {
  background: var(--gradient-secondary);
}

.progress-success {
  background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
}

.progress-warning {
  background: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
}

.progress-error {
  background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
}

.progress-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  min-width: 40px;
  text-align: right;
}

/* Animations */
@keyframes spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes progress-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
