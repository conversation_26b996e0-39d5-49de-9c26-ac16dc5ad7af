/* Enhanced Toast Styles */
.toast-container {
  z-index: var(--z-toast);
}

.custom-toast {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-gray-200);
  cursor: pointer;
  transition: all var(--transition-base);
  max-width: 400px;
  min-width: 300px;
  position: relative;
  overflow: hidden;
}

.custom-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-gray-300);
  transition: background var(--transition-base);
}

.custom-toast:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

/* Toast Types */
.custom-toast.success {
  border-color: var(--color-success-200);
  background: var(--color-success-50);
}

.custom-toast.success::before {
  background: var(--gradient-secondary);
}

.custom-toast.error {
  border-color: var(--color-error-200);
  background: var(--color-error-50);
}

.custom-toast.error::before {
  background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
}

.custom-toast.warning {
  border-color: var(--color-warning-200);
  background: var(--color-warning-50);
}

.custom-toast.warning::before {
  background: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
}

.custom-toast.info {
  border-color: var(--color-info-200);
  background: var(--color-info-50);
}

.custom-toast.info::before {
  background: var(--gradient-primary);
}

.custom-toast.loading {
  border-color: var(--color-primary-200);
  background: var(--color-primary-50);
}

.custom-toast.loading::before {
  background: var(--gradient-primary);
  animation: loading-progress 2s ease-in-out infinite;
}

/* Toast Icon */
.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  margin-top: var(--space-1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Toast Content */
.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-1);
  font-size: var(--font-size-sm);
}

.toast-message {
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  word-wrap: break-word;
}

/* Toast Close Button */
.toast-close {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.toast-close:hover {
  color: var(--color-gray-600);
  background: var(--color-gray-100);
  transform: scale(1.1);
}

/* Loading Spinner for Toast */
.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-primary-200);
  border-top: 2px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Toast Animations */
.toast-enter {
  animation: toast-slide-in 0.3s ease-out;
}

.toast-exit {
  animation: toast-slide-out 0.2s ease-in;
}

@keyframes toast-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes loading-progress {
  0% {
    transform: scaleX(0);
    transform-origin: left;
  }
  50% {
    transform: scaleX(1);
    transform-origin: left;
  }
  51% {
    transform: scaleX(1);
    transform-origin: right;
  }
  100% {
    transform: scaleX(0);
    transform-origin: right;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .custom-toast {
  background: var(--color-gray-800);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .custom-toast.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: var(--color-success-700);
}

[data-theme="dark"] .custom-toast.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--color-error-700);
}

[data-theme="dark"] .custom-toast.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--color-warning-700);
}

[data-theme="dark"] .custom-toast.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--color-info-700);
}

[data-theme="dark"] .custom-toast.loading {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--color-primary-700);
}

[data-theme="dark"] .toast-title {
  color: var(--color-gray-100);
}

[data-theme="dark"] .toast-message {
  color: var(--color-gray-300);
}

[data-theme="dark"] .toast-close {
  color: var(--color-gray-500);
}

[data-theme="dark"] .toast-close:hover {
  color: var(--color-gray-300);
  background: var(--color-gray-700);
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-toast {
    min-width: 280px;
    max-width: calc(100vw - 40px);
    margin: 0 var(--space-4);
  }
  
  .toast-title {
    font-size: var(--font-size-xs);
  }
  
  .toast-message {
    font-size: var(--font-size-xs);
  }
  
  .toast-icon {
    font-size: var(--font-size-base);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .toast-enter,
  .toast-exit {
    animation: none;
  }
  
  .custom-toast:hover {
    transform: none;
  }
  
  .loading-spinner-small {
    animation: none;
  }
  
  @keyframes loading-progress {
    0%, 100% {
      transform: scaleX(0.5);
    }
  }
}
