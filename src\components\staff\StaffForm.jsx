import React, { useState, useEffect } from 'react';
import staffService, { STAFF_STATUS, STAFF_ROLES, COMMISSION_TYPES } from '../../services/staffService';
import serviceService from '../../services/serviceService';
import toast from 'react-hot-toast';
import './StaffForm.css';

const StaffForm = ({ staff, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: STAFF_ROLES.STYLIST,
    status: STAFF_STATUS.ACTIVE,
    hireDate: new Date().toISOString().split('T')[0],
    dateOfBirth: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    hourlyRate: 0,
    commission: {
      type: COMMISSION_TYPES.PERCENTAGE,
      rate: 0,
      minimumSales: 0
    },
    specialties: [],
    skills: [],
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [services, setServices] = useState([]);
  const [availableSpecialties] = useState([
    'Hair Cutting', 'Hair Coloring', 'Hair Styling', 'Perms', 'Hair Extensions',
    'Facial Treatments', 'Eyebrow Shaping', 'Eyelash Extensions', 'Makeup Application',
    'Manicures', 'Pedicures', 'Nail Art', 'Gel Nails', 'Acrylic Nails',
    'Swedish Massage', 'Deep Tissue Massage', 'Hot Stone Massage', 'Aromatherapy',
    'Customer Service', 'Appointment Scheduling', 'Point of Sale', 'Inventory Management'
  ]);

  useEffect(() => {
    // Load services for skills
    const servicesData = serviceService.getAllServices();
    setServices(servicesData);

    // If editing existing staff, populate form
    if (staff) {
      setFormData({
        firstName: staff.firstName || '',
        lastName: staff.lastName || '',
        email: staff.email || '',
        phone: staff.phone || '',
        role: staff.role || STAFF_ROLES.STYLIST,
        status: staff.status || STAFF_STATUS.ACTIVE,
        hireDate: staff.hireDate || new Date().toISOString().split('T')[0],
        dateOfBirth: staff.dateOfBirth || '',
        address: staff.address || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        hourlyRate: staff.hourlyRate || 0,
        commission: staff.commission || {
          type: COMMISSION_TYPES.PERCENTAGE,
          rate: 0,
          minimumSales: 0
        },
        specialties: staff.specialties || [],
        skills: staff.skills || [],
        notes: staff.notes || ''
      });
    }
  }, [staff]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (formData.hourlyRate < 0) {
      newErrors.hourlyRate = 'Hourly rate cannot be negative';
    }

    if (!formData.hireDate) {
      newErrors.hireDate = 'Hire date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'number' ? parseFloat(value) || 0 : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) || 0 : value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSpecialtyToggle = (specialty) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties.includes(specialty)
        ? prev.specialties.filter(s => s !== specialty)
        : [...prev.specialties, specialty]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      if (staff) {
        // Update existing staff
        await staffService.updateStaff(staff.id, formData);
        toast.success('Staff member updated successfully');
      } else {
        // Create new staff
        await staffService.addStaff(formData);
        toast.success('Staff member added successfully');
      }
      
      onSave();
    } catch (error) {
      console.error('Error saving staff:', error);
      toast.error('Failed to save staff member');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="staff-form">
      <div className="form-header">
        <h2 className="form-title">
          <span className="title-icon">👤</span>
          {staff ? 'Edit Staff Member' : 'Add New Staff Member'}
        </h2>
        <p className="form-subtitle">
          {staff ? 'Update staff member information' : 'Enter details for the new staff member'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="staff-form-content">
        <div className="form-grid">
          {/* Personal Information */}
          <div className="form-section">
            <h3 className="section-title">Personal Information</h3>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="firstName" className="form-label">First Name *</label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className={`form-input ${errors.firstName ? 'form-input-error' : ''}`}
                  placeholder="Enter first name"
                />
                {errors.firstName && <span className="error-message">{errors.firstName}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="lastName" className="form-label">Last Name *</label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className={`form-input ${errors.lastName ? 'form-input-error' : ''}`}
                  placeholder="Enter last name"
                />
                {errors.lastName && <span className="error-message">{errors.lastName}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`form-input ${errors.email ? 'form-input-error' : ''}`}
                  placeholder="Enter email address"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="phone" className="form-label">Phone *</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={`form-input ${errors.phone ? 'form-input-error' : ''}`}
                  placeholder="Enter phone number"
                />
                {errors.phone && <span className="error-message">{errors.phone}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="dateOfBirth" className="form-label">Date of Birth</label>
                <input
                  type="date"
                  id="dateOfBirth"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label htmlFor="hireDate" className="form-label">Hire Date *</label>
                <input
                  type="date"
                  id="hireDate"
                  name="hireDate"
                  value={formData.hireDate}
                  onChange={handleInputChange}
                  className={`form-input ${errors.hireDate ? 'form-input-error' : ''}`}
                />
                {errors.hireDate && <span className="error-message">{errors.hireDate}</span>}
              </div>
            </div>
          </div>

          {/* Employment Information */}
          <div className="form-section">
            <h3 className="section-title">Employment Information</h3>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="role" className="form-label">Role</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  {Object.entries(STAFF_ROLES).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="status" className="form-label">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  {Object.entries(STAFF_STATUS).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="hourlyRate" className="form-label">Hourly Rate ($)</label>
                <input
                  type="number"
                  id="hourlyRate"
                  name="hourlyRate"
                  value={formData.hourlyRate}
                  onChange={handleInputChange}
                  className={`form-input ${errors.hourlyRate ? 'form-input-error' : ''}`}
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
                {errors.hourlyRate && <span className="error-message">{errors.hourlyRate}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="commission.type" className="form-label">Commission Type</label>
                <select
                  id="commission.type"
                  name="commission.type"
                  value={formData.commission.type}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  {Object.entries(COMMISSION_TYPES).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="commission.rate" className="form-label">
                  Commission Rate {formData.commission.type === 'percentage' ? '(%)' : '($)'}
                </label>
                <input
                  type="number"
                  id="commission.rate"
                  name="commission.rate"
                  value={formData.commission.rate}
                  onChange={handleInputChange}
                  className="form-input"
                  min="0"
                  step={formData.commission.type === 'percentage' ? '1' : '0.01'}
                  placeholder="0"
                />
              </div>

              <div className="form-group">
                <label htmlFor="commission.minimumSales" className="form-label">Minimum Sales ($)</label>
                <input
                  type="number"
                  id="commission.minimumSales"
                  name="commission.minimumSales"
                  value={formData.commission.minimumSales}
                  onChange={handleInputChange}
                  className="form-input"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="btn-spinner"></span>
                {staff ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              <>
                <span className="btn-icon">{staff ? '💾' : '➕'}</span>
                {staff ? 'Update Staff' : 'Add Staff'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StaffForm;
