import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';
import ThemeToggle from '../common/ThemeToggle';
import toast from 'react-hot-toast';
import './DashboardLayout.css';

const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/signin');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
  };

  // Navigation items based on user role
  const getNavigationItems = () => {
    const items = [
      {
        name: 'Dashboard',
        path: '/dashboard',
        icon: '📊',
        permission: 'view_dashboard'
      }
    ];

    if (authService.canAccessCustomers()) {
      items.push({
        name: 'Customers',
        path: '/dashboard/customers',
        icon: '👥',
        permission: 'manage_customers'
      });
    }

    if (authService.canAccessAppointments()) {
      items.push({
        name: 'Appointments',
        path: '/dashboard/appointments',
        icon: '📅',
        permission: 'manage_appointments'
      });
    }

    if (authService.canAccessServices()) {
      items.push({
        name: 'Services',
        path: '/dashboard/services',
        icon: '✂️',
        permission: 'manage_services'
      });
    }

    if (authService.canAccessStaff()) {
      items.push({
        name: 'Staff',
        path: '/dashboard/staff',
        icon: '👨‍💼',
        permission: 'manage_staff'
      });
    }

    if (authService.canAccessBilling()) {
      items.push({
        name: 'Billing',
        path: '/dashboard/billing',
        icon: '💰',
        permission: 'manage_billing'
      });
    }

    if (authService.canAccessInventory()) {
      items.push({
        name: 'Inventory',
        path: '/dashboard/inventory',
        icon: '📦',
        permission: 'manage_inventory'
      });
    }

    if (authService.canAccessReports()) {
      items.push({
        name: 'Reports',
        path: '/dashboard/reports',
        icon: '📈',
        permission: 'view_reports'
      });
    }

    return items;
  };

  const navigationItems = getNavigationItems();
  const userDisplayInfo = authService.getUserDisplayInfo();

  return (
    <div className="dashboard-layout">
      {/* Sidebar */}
      <aside className={`sidebar ${sidebarOpen ? 'sidebar-open' : 'sidebar-closed'}`}>
        <div className="sidebar-header">
          <div className="logo">
            <span className="logo-icon">💇‍♀️</span>
            {sidebarOpen && <span className="logo-text">Salon Manager</span>}
          </div>
        </div>

        <nav className="sidebar-nav">
          {navigationItems.map((item) => (
            <button
              key={item.path}
              onClick={() => navigate(item.path)}
              className={`nav-item ${location.pathname === item.path ? 'nav-item-active' : ''}`}
              title={!sidebarOpen ? item.name : ''}
            >
              <span className="nav-icon">{item.icon}</span>
              {sidebarOpen && <span className="nav-text">{item.name}</span>}
            </button>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <div className="main-content">
        {/* Header */}
        <header className="dashboard-header">
          <div className="header-left">
            <button className="sidebar-toggle" onClick={toggleSidebar}>
              <span className="hamburger-icon">☰</span>
            </button>
            <h1 className="page-title">
              {navigationItems.find(item => item.path === location.pathname)?.name || 'Dashboard'}
            </h1>
          </div>

          <div className="header-right">
            {/* Theme Toggle */}
            <ThemeToggle />

            {/* User Menu */}
            <div className="user-menu">
              <button className="user-menu-trigger" onClick={toggleUserMenu}>
                <div className="user-avatar">
                  {userDisplayInfo?.initials || 'U'}
                </div>
                <div className="user-info">
                  <span className="user-name">{userDisplayInfo?.name || 'User'}</span>
                  <span className="user-role">{authService.formatRole(user?.role)}</span>
                </div>
                <span className="dropdown-arrow">▼</span>
              </button>

              {userMenuOpen && (
                <div className="user-menu-dropdown">
                  <div className="user-menu-header">
                    <div className="user-avatar-large">
                      {userDisplayInfo?.initials || 'U'}
                    </div>
                    <div className="user-details">
                      <div className="user-name-large">{userDisplayInfo?.name}</div>
                      <div className="user-email">{userDisplayInfo?.email}</div>
                      <div className="user-role-badge" style={{ backgroundColor: authService.getRoleColor(user?.role) }}>
                        {authService.formatRole(user?.role)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="user-menu-items">
                    <button className="user-menu-item" onClick={() => navigate('/dashboard/profile')}>
                      <span className="menu-item-icon">👤</span>
                      Profile Settings
                    </button>
                    <button className="user-menu-item" onClick={handleLogout}>
                      <span className="menu-item-icon">🚪</span>
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="page-content">
          <Outlet />
        </main>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div className="sidebar-overlay" onClick={() => setSidebarOpen(false)}></div>
      )}
    </div>
  );
};

export default DashboardLayout;
