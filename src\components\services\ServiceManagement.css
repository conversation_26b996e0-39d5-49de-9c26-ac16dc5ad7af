/* Service Management Styles - Modern UI */
.service-management {
  padding: 0;
  max-width: 100%;
  margin: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
}

.service-management::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Header Styles */
.service-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 0 0 32px 32px;
  padding: var(--space-8) var(--space-6);
  margin-bottom: var(--space-8);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 var(--space-3) 0;
  display: flex;
  align-items: center;
  gap: var(--space-4);
  letter-spacing: -0.02em;
}

.title-icon {
  font-size: clamp(2.5rem, 5vw, 4rem);
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
}

.page-subtitle {
  color: #64748b;
  font-size: var(--text-lg);
  margin: 0;
  font-weight: 500;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

/* Navigation Tabs */
.service-nav {
  display: flex;
  gap: var(--space-1);
  background: rgba(255, 255, 255, 0.6);
  padding: var(--space-2);
  border-radius: 20px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 1400px;
  margin: 0 auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: none;
  border-radius: 16px;
  color: #64748b;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-size: var(--text-sm);
  white-space: nowrap;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #334155;
  transform: translateY(-1px);
}

.nav-tab-active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  font-weight: 700;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.nav-tab-active::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

.tab-icon {
  font-size: var(--text-lg);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 var(--space-6);
  position: relative;
  z-index: 1;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: var(--space-8);
  display: flex;
  align-items: center;
  gap: var(--space-5);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  font-size: var(--text-4xl);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 20px;
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
  position: relative;
}

.stat-icon::before {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 18px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin-bottom: var(--space-2);
  letter-spacing: -0.02em;
}

.stat-label {
  color: #64748b;
  font-size: var(--text-base);
  font-weight: 600;
  opacity: 0.8;
}

/* Service Content */
.service-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 32px;
  padding: var(--space-8);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto var(--space-8) auto;
  position: relative;
  z-index: 1;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--space-4);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  transform: translateY(-2px);
  box-shadow:
    0 20px 25px -5px rgba(59, 130, 246, 0.4),
    0 10px 10px -5px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #475569;
  border: 1px solid rgba(203, 213, 225, 0.5);
  backdrop-filter: blur(12px);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1);
  color: #334155;
  transform: translateY(-2px);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(239, 68, 68, 0.3),
    0 4px 6px -2px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow:
    0 20px 25px -5px rgba(239, 68, 68, 0.4),
    0 10px 10px -5px rgba(239, 68, 68, 0.3);
}

.btn-sm {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-xs);
  border-radius: 12px;
}

.btn-icon {
  font-size: var(--text-base);
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-header {
    border-radius: 0 0 24px 24px;
    padding: var(--space-6) var(--space-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    padding: 0 var(--space-4);
  }

  .service-nav {
    flex-wrap: wrap;
    margin: 0 var(--space-4);
  }

  .service-content {
    margin: 0 var(--space-4) var(--space-6) var(--space-4);
    padding: var(--space-6);
  }

  .page-title {
    font-size: clamp(1.5rem, 3vw, 2rem);
  }

  .title-icon {
    font-size: clamp(2rem, 4vw, 2.5rem);
  }
}

@media (max-width: 480px) {
  .service-header {
    padding: var(--space-4) var(--space-3);
    border-radius: 0 0 20px 20px;
  }

  .service-content {
    padding: var(--space-4);
    border-radius: 20px;
    margin: 0 var(--space-3) var(--space-4) var(--space-3);
  }

  .stats-grid {
    padding: 0 var(--space-3);
    gap: var(--space-4);
  }

  .stat-card {
    padding: var(--space-6);
    gap: var(--space-3);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: var(--text-2xl);
  }

  .service-nav {
    margin: 0 var(--space-3);
    padding: var(--space-1);
  }

  .nav-tab {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }

  .btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-xs);
  }

  .btn-sm {
    padding: var(--space-2) var(--space-3);
  }
}
