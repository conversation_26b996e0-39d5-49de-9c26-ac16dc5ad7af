import React, { useState, useEffect } from 'react';
import serviceService, { SERVICE_CATEGORIES, SERVICE_STATUS } from '../../services/serviceService';
import './ServiceForm.css';

const ServiceForm = ({ service, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    category: SERVICE_CATEGORIES.HAIR,
    description: '',
    duration: 60,
    price: 0,
    status: SERVICE_STATUS.ACTIVE,
    requirements: [],
    staffRequired: 1,
    popularity: 50
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [requirementInput, setRequirementInput] = useState('');

  const categories = serviceService.getCategories();

  useEffect(() => {
    if (service) {
      setFormData({
        name: service.name || '',
        category: service.category || SERVICE_CATEGORIES.HAIR,
        description: service.description || '',
        duration: service.duration || 60,
        price: service.price || 0,
        status: service.status || SERVICE_STATUS.ACTIVE,
        requirements: service.requirements || [],
        staffRequired: service.staffRequired || 1,
        popularity: service.popularity || 50
      });
    }
  }, [service]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Service description is required';
    }

    if (formData.duration <= 0) {
      newErrors.duration = 'Duration must be greater than 0';
    }

    if (formData.price < 0) {
      newErrors.price = 'Price cannot be negative';
    }

    if (formData.staffRequired <= 0) {
      newErrors.staffRequired = 'Staff required must be at least 1';
    }

    if (formData.popularity < 0 || formData.popularity > 100) {
      newErrors.popularity = 'Popularity must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleAddRequirement = () => {
    if (requirementInput.trim() && !formData.requirements.includes(requirementInput.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, requirementInput.trim()]
      }));
      setRequirementInput('');
    }
  };

  const handleRemoveRequirement = (index) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving service:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`;
    }
    return `${mins}m`;
  };

  return (
    <div className="service-form">
      <div className="form-header">
        <h2 className="form-title">
          <span className="title-icon">
            {service ? '✏️' : '➕'}
          </span>
          {service ? 'Edit Service' : 'Add New Service'}
        </h2>
        <p className="form-subtitle">
          {service ? 'Update service information' : 'Create a new service for your salon'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="service-form-content">
        <div className="form-grid">
          {/* Basic Information */}
          <div className="form-section">
            <h3 className="section-title">Basic Information</h3>
            
            <div className="form-group">
              <label htmlFor="name" className="form-label">
                Service Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`form-input ${errors.name ? 'form-input-error' : ''}`}
                placeholder="Enter service name"
              />
              {errors.name && <span className="error-message">{errors.name}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="category" className="form-label">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="form-select"
              >
                {Object.entries(categories).map(([key, category]) => (
                  <option key={key} value={key}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="description" className="form-label">
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className={`form-textarea ${errors.description ? 'form-input-error' : ''}`}
                placeholder="Describe the service"
                rows="3"
              />
              {errors.description && <span className="error-message">{errors.description}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="status" className="form-label">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value={SERVICE_STATUS.ACTIVE}>Active</option>
                <option value={SERVICE_STATUS.INACTIVE}>Inactive</option>
                <option value={SERVICE_STATUS.DISCONTINUED}>Discontinued</option>
              </select>
            </div>
          </div>

          {/* Pricing & Duration */}
          <div className="form-section">
            <h3 className="section-title">Pricing & Duration</h3>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="price" className="form-label">
                  Price ($) *
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className={`form-input ${errors.price ? 'form-input-error' : ''}`}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
                {errors.price && <span className="error-message">{errors.price}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="duration" className="form-label">
                  Duration (minutes) *
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className={`form-input ${errors.duration ? 'form-input-error' : ''}`}
                  placeholder="60"
                  min="1"
                  step="15"
                />
                {errors.duration && <span className="error-message">{errors.duration}</span>}
                <small className="form-help">
                  Duration: {formatDuration(formData.duration)}
                </small>
              </div>
            </div>
          </div>

          {/* Service Details */}
          <div className="form-section">
            <h3 className="section-title">Service Details</h3>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="staffRequired" className="form-label">
                  Staff Required
                </label>
                <input
                  type="number"
                  id="staffRequired"
                  name="staffRequired"
                  value={formData.staffRequired}
                  onChange={handleInputChange}
                  className={`form-input ${errors.staffRequired ? 'form-input-error' : ''}`}
                  min="1"
                />
                {errors.staffRequired && <span className="error-message">{errors.staffRequired}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="popularity" className="form-label">
                  Popularity (0-100)
                </label>
                <input
                  type="number"
                  id="popularity"
                  name="popularity"
                  value={formData.popularity}
                  onChange={handleInputChange}
                  className={`form-input ${errors.popularity ? 'form-input-error' : ''}`}
                  min="0"
                  max="100"
                />
                {errors.popularity && <span className="error-message">{errors.popularity}</span>}
              </div>
            </div>

            {/* Requirements */}
            <div className="form-group">
              <label className="form-label">
                Requirements
              </label>
              <div className="requirements-input">
                <input
                  type="text"
                  value={requirementInput}
                  onChange={(e) => setRequirementInput(e.target.value)}
                  className="form-input"
                  placeholder="Add a requirement"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddRequirement())}
                />
                <button
                  type="button"
                  onClick={handleAddRequirement}
                  className="btn btn-secondary btn-sm"
                >
                  Add
                </button>
              </div>
              
              {formData.requirements.length > 0 && (
                <div className="requirements-list">
                  {formData.requirements.map((requirement, index) => (
                    <div key={index} className="requirement-tag">
                      <span>{requirement}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveRequirement(index)}
                        className="requirement-remove"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="btn-spinner"></span>
                {service ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <span className="btn-icon">💾</span>
                {service ? 'Update Service' : 'Create Service'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ServiceForm;
