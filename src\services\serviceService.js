// Service Management Service for Salon Management System
import { v4 as uuidv4 } from 'uuid';

// Service Categories
export const SERVICE_CATEGORIES = {
  HAIR: 'hair',
  BEAUTY: 'beauty',
  NAILS: 'nails',
  MASSAGE: 'massage',
  PACKAGES: 'packages'
};

// Service Status
export const SERVICE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DISCONTINUED: 'discontinued'
};

// Default Service Categories with metadata
export const CATEGORY_INFO = {
  [SERVICE_CATEGORIES.HAIR]: {
    name: 'Hair Services',
    icon: '💇‍♀️',
    description: 'Professional hair cutting, styling, and treatment services',
    color: '#8B5CF6'
  },
  [SERVICE_CATEGORIES.BEAUTY]: {
    name: 'Beauty Services',
    icon: '✨',
    description: 'Facial treatments, eyebrow services, and beauty enhancements',
    color: '#EC4899'
  },
  [SERVICE_CATEGORIES.NAILS]: {
    name: 'Nail Services',
    icon: '💅',
    description: 'Manicure, pedicure, and nail art services',
    color: '#F59E0B'
  },
  [SERVICE_CATEGORIES.MASSAGE]: {
    name: 'Massage & Wellness',
    icon: '🧘‍♀️',
    description: 'Relaxation and therapeutic massage services',
    color: '#10B981'
  },
  [SERVICE_CATEGORIES.PACKAGES]: {
    name: 'Service Packages',
    icon: '🎁',
    description: 'Bundled services and special deals',
    color: '#6366F1'
  }
};

class ServiceService {
  constructor() {
    this.STORAGE_KEY = 'salon_services';
    this.PACKAGES_KEY = 'salon_packages';
    this.services = this.loadServices();
    this.packages = this.loadPackages();
    this.nextId = this.getNextId();
  }

  // Load services from localStorage or generate mock data
  loadServices() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading services:', error);
    }
    
    // Generate initial mock data
    const mockServices = this.generateMockServices();
    this.saveServices(mockServices);
    return mockServices;
  }

  // Load packages from localStorage or generate mock data
  loadPackages() {
    try {
      const stored = localStorage.getItem(this.PACKAGES_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading packages:', error);
    }
    
    // Generate initial mock data
    const mockPackages = this.generateMockPackages();
    this.savePackages(mockPackages);
    return mockPackages;
  }

  // Generate mock services data
  generateMockServices() {
    return [
      // Hair Services
      {
        id: 'service_1',
        name: 'Classic Haircut',
        category: SERVICE_CATEGORIES.HAIR,
        description: 'Professional haircut with wash and style',
        duration: 60,
        price: 50,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Basic hair cutting skills'],
        staffRequired: 1,
        popularity: 95,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_2',
        name: 'Hair Wash & Blow Dry',
        category: SERVICE_CATEGORIES.HAIR,
        description: 'Professional hair washing and blow drying service',
        duration: 45,
        price: 30,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Hair washing techniques'],
        staffRequired: 1,
        popularity: 80,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_3',
        name: 'Hair Coloring',
        category: SERVICE_CATEGORIES.HAIR,
        description: 'Full hair coloring service with premium products',
        duration: 120,
        price: 100,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Color theory', 'Chemical processing'],
        staffRequired: 1,
        popularity: 75,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_4',
        name: 'Highlights',
        category: SERVICE_CATEGORIES.HAIR,
        description: 'Professional highlighting with foil technique',
        duration: 150,
        price: 120,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Advanced coloring', 'Foil techniques'],
        staffRequired: 1,
        popularity: 70,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_5',
        name: 'Perm',
        category: SERVICE_CATEGORIES.HAIR,
        description: 'Chemical perm for lasting curls',
        duration: 180,
        price: 80,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Chemical processing', 'Perm techniques'],
        staffRequired: 1,
        popularity: 40,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      // Beauty Services
      {
        id: 'service_6',
        name: 'Facial Treatment',
        category: SERVICE_CATEGORIES.BEAUTY,
        description: 'Deep cleansing facial with moisturizing treatment',
        duration: 90,
        price: 70,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Skincare knowledge', 'Facial techniques'],
        staffRequired: 1,
        popularity: 85,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_7',
        name: 'Eyebrow Threading',
        category: SERVICE_CATEGORIES.BEAUTY,
        description: 'Precise eyebrow shaping using threading technique',
        duration: 30,
        price: 15,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Threading skills', 'Eyebrow shaping'],
        staffRequired: 1,
        popularity: 90,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      // Nail Services
      {
        id: 'service_8',
        name: 'Manicure',
        category: SERVICE_CATEGORIES.NAILS,
        description: 'Complete manicure with nail polish',
        duration: 45,
        price: 25,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Nail care techniques'],
        staffRequired: 1,
        popularity: 88,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'service_9',
        name: 'Pedicure',
        category: SERVICE_CATEGORIES.NAILS,
        description: 'Complete pedicure with foot massage',
        duration: 60,
        price: 35,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Foot care techniques'],
        staffRequired: 1,
        popularity: 82,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      // Massage Services
      {
        id: 'service_10',
        name: 'Head Massage',
        category: SERVICE_CATEGORIES.MASSAGE,
        description: 'Relaxing head and scalp massage',
        duration: 30,
        price: 20,
        status: SERVICE_STATUS.ACTIVE,
        requirements: ['Massage techniques'],
        staffRequired: 1,
        popularity: 65,
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      }
    ];
  }

  // Generate mock packages data
  generateMockPackages() {
    return [
      {
        id: 'package_1',
        name: 'Bridal Beauty Package',
        description: 'Complete bridal makeover package',
        services: ['service_1', 'service_3', 'service_6', 'service_7', 'service_8'],
        originalPrice: 260,
        packagePrice: 220,
        discount: 40,
        duration: 300,
        status: SERVICE_STATUS.ACTIVE,
        popularity: 95,
        validUntil: new Date('2024-12-31').toISOString(),
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      },
      {
        id: 'package_2',
        name: 'Pamper Package',
        description: 'Relaxing spa day package',
        services: ['service_6', 'service_8', 'service_9', 'service_10'],
        originalPrice: 150,
        packagePrice: 120,
        discount: 30,
        duration: 225,
        status: SERVICE_STATUS.ACTIVE,
        popularity: 80,
        validUntil: new Date('2024-12-31').toISOString(),
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString()
      }
    ];
  }

  // Save services to localStorage
  saveServices(services = this.services) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(services));
    } catch (error) {
      console.error('Error saving services:', error);
    }
  }

  // Save packages to localStorage
  savePackages(packages = this.packages) {
    try {
      localStorage.setItem(this.PACKAGES_KEY, JSON.stringify(packages));
    } catch (error) {
      console.error('Error saving packages:', error);
    }
  }

  // Get next ID for new services
  getNextId() {
    const maxId = Math.max(
      ...this.services.map(s => parseInt(s.id.replace('service_', '')) || 0),
      0
    );
    return maxId + 1;
  }

  // Get all services
  getAllServices() {
    return [...this.services];
  }

  // Get services by category
  getServicesByCategory(category) {
    return this.services.filter(service => service.category === category);
  }

  // Get service by ID
  getServiceById(id) {
    return this.services.find(service => service.id === id);
  }

  // Get active services
  getActiveServices() {
    return this.services.filter(service => service.status === SERVICE_STATUS.ACTIVE);
  }

  // Search services
  searchServices(query) {
    const searchTerm = query.toLowerCase();
    return this.services.filter(service =>
      service.name.toLowerCase().includes(searchTerm) ||
      service.description.toLowerCase().includes(searchTerm) ||
      service.category.toLowerCase().includes(searchTerm)
    );
  }

  // Add new service
  addService(serviceData) {
    const newService = {
      id: `service_${this.nextId}`,
      ...serviceData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.services.push(newService);
    this.nextId++;
    this.saveServices();
    return newService;
  }

  // Update service
  updateService(id, updates) {
    const index = this.services.findIndex(service => service.id === id);
    if (index === -1) {
      throw new Error('Service not found');
    }

    this.services[index] = {
      ...this.services[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveServices();
    return this.services[index];
  }

  // Delete service
  deleteService(id) {
    const index = this.services.findIndex(service => service.id === id);
    if (index === -1) {
      throw new Error('Service not found');
    }

    this.services.splice(index, 1);
    this.saveServices();
    return true;
  }

  // Get all packages
  getAllPackages() {
    return [...this.packages];
  }

  // Get package by ID
  getPackageById(id) {
    return this.packages.find(pkg => pkg.id === id);
  }

  // Add new package
  addPackage(packageData) {
    const newPackage = {
      id: `package_${uuidv4()}`,
      ...packageData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.packages.push(newPackage);
    this.savePackages();
    return newPackage;
  }

  // Update package
  updatePackage(id, updates) {
    const index = this.packages.findIndex(pkg => pkg.id === id);
    if (index === -1) {
      throw new Error('Package not found');
    }

    this.packages[index] = {
      ...this.packages[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.savePackages();
    return this.packages[index];
  }

  // Delete package
  deletePackage(id) {
    const index = this.packages.findIndex(pkg => pkg.id === id);
    if (index === -1) {
      throw new Error('Package not found');
    }

    this.packages.splice(index, 1);
    this.savePackages();
    return true;
  }

  // Get service statistics
  getServiceStats() {
    const totalServices = this.services.length;
    const activeServices = this.services.filter(s => s.status === SERVICE_STATUS.ACTIVE).length;
    const totalPackages = this.packages.length;
    
    const categoryStats = {};
    Object.values(SERVICE_CATEGORIES).forEach(category => {
      categoryStats[category] = this.services.filter(s => s.category === category).length;
    });

    const avgPrice = this.services.reduce((sum, service) => sum + service.price, 0) / totalServices;
    const avgDuration = this.services.reduce((sum, service) => sum + service.duration, 0) / totalServices;

    return {
      totalServices,
      activeServices,
      totalPackages,
      categoryStats,
      avgPrice: Math.round(avgPrice * 100) / 100,
      avgDuration: Math.round(avgDuration)
    };
  }

  // Get categories
  getCategories() {
    return CATEGORY_INFO;
  }
}

// Create and export singleton instance
const serviceService = new ServiceService();
export default serviceService;
