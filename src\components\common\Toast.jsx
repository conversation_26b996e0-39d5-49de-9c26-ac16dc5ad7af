import React from 'react';
import toast, { Toaster } from 'react-hot-toast';
import './Toast.css';

// Enhanced Toast Component with custom styling
const CustomToast = ({ t, type, title, message, icon }) => (
  <div
    className={`custom-toast ${type} ${t.visible ? 'toast-enter' : 'toast-exit'}`}
    onClick={() => toast.dismiss(t.id)}
  >
    <div className="toast-icon">
      {icon}
    </div>
    <div className="toast-content">
      {title && <div className="toast-title">{title}</div>}
      <div className="toast-message">{message}</div>
    </div>
    <button
      className="toast-close"
      onClick={(e) => {
        e.stopPropagation();
        toast.dismiss(t.id);
      }}
    >
      ✕
    </button>
  </div>
);

// Enhanced Toast Functions
export const showToast = {
  success: (message, title = 'Success') => {
    toast.custom((t) => (
      <CustomToast
        t={t}
        type="success"
        title={title}
        message={message}
        icon="✅"
      />
    ), {
      duration: 4000,
      position: 'top-right',
    });
  },

  error: (message, title = 'Error') => {
    toast.custom((t) => (
      <CustomToast
        t={t}
        type="error"
        title={title}
        message={message}
        icon="❌"
      />
    ), {
      duration: 6000,
      position: 'top-right',
    });
  },

  warning: (message, title = 'Warning') => {
    toast.custom((t) => (
      <CustomToast
        t={t}
        type="warning"
        title={title}
        message={message}
        icon="⚠️"
      />
    ), {
      duration: 5000,
      position: 'top-right',
    });
  },

  info: (message, title = 'Info') => {
    toast.custom((t) => (
      <CustomToast
        t={t}
        type="info"
        title={title}
        message={message}
        icon="ℹ️"
      />
    ), {
      duration: 4000,
      position: 'top-right',
    });
  },

  loading: (message, title = 'Loading') => {
    return toast.custom((t) => (
      <CustomToast
        t={t}
        type="loading"
        title={title}
        message={message}
        icon={<div className="loading-spinner-small" />}
      />
    ), {
      duration: Infinity,
      position: 'top-right',
    });
  },

  promise: (promise, messages) => {
    return toast.promise(promise, {
      loading: messages.loading || 'Loading...',
      success: messages.success || 'Success!',
      error: messages.error || 'Error occurred',
    }, {
      style: {
        minWidth: '250px',
      },
      success: {
        duration: 4000,
        icon: '✅',
      },
      error: {
        duration: 6000,
        icon: '❌',
      },
    });
  }
};

// Enhanced Toaster Component
export const EnhancedToaster = () => (
  <Toaster
    position="top-right"
    reverseOrder={false}
    gutter={8}
    containerClassName="toast-container"
    containerStyle={{
      top: 20,
      right: 20,
    }}
    toastOptions={{
      duration: 4000,
      style: {
        background: 'var(--color-gray-50)',
        color: 'var(--color-gray-900)',
        border: '1px solid var(--color-gray-200)',
        borderRadius: 'var(--radius-xl)',
        boxShadow: 'var(--shadow-lg)',
        padding: '16px',
        fontSize: 'var(--font-size-sm)',
        fontWeight: 'var(--font-weight-medium)',
        maxWidth: '400px',
      },
      success: {
        iconTheme: {
          primary: 'var(--color-success-500)',
          secondary: 'white',
        },
        style: {
          border: '1px solid var(--color-success-200)',
          background: 'var(--color-success-50)',
        },
      },
      error: {
        iconTheme: {
          primary: 'var(--color-error-500)',
          secondary: 'white',
        },
        style: {
          border: '1px solid var(--color-error-200)',
          background: 'var(--color-error-50)',
        },
      },
      loading: {
        iconTheme: {
          primary: 'var(--color-primary-500)',
          secondary: 'white',
        },
        style: {
          border: '1px solid var(--color-primary-200)',
          background: 'var(--color-primary-50)',
        },
      },
    }}
  />
);

export default showToast;
