/* Modern Contact Hero Section */
.contact-hero-section {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-top: 80px;
}

.contact-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-accent-600) 0%, var(--color-primary-700) 100%);
  z-index: 1;
}

.contact-hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-pattern)"/></svg>');
  opacity: 0.3;
}

.contact-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%);
  z-index: 2;
}

.contact-hero-content {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

.contact-hero-text {
  max-width: 600px;
  color: white;
}

.contact-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: 2rem;
}

.contact-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.contact-hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  opacity: 0.9;
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-accent-100));
  color: var(--color-primary-700);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: 1rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Contact Info Section */
.contact-info-section {
  padding: 6rem 2rem;
  background: var(--color-gray-50);
}

.contact-info-container {
  max-width: 1400px;
  margin: 0 auto;
}

.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.contact-info-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.contact-info-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.contact-info-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
}

.contact-info-icon svg {
  width: 32px;
  height: 32px;
}

.contact-info-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.contact-info-details {
  color: var(--color-gray-600);
  line-height: 1.6;
}

.contact-info-details p {
  margin-bottom: 0.5rem;
}

/* Contact Form Section */
.contact-form-section {
  padding: 6rem 2rem;
  background: white;
}

.contact-form-container {
  max-width: 1400px;
  margin: 0 auto;
}

.contact-form-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 4rem;
  align-items: start;
}

.contact-form-text {
  max-width: 500px;
}

.contact-features {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-accent-100));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary-600);
  flex-shrink: 0;
}

.feature-icon svg {
  width: 24px;
  height: 24px;
}

.feature-text h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 0.25rem;
}

.feature-text p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: 1.5;
}

.contact-form-card {
  background: var(--color-gray-50);
  padding: 3rem;
  border-radius: 24px;
  border: 1px solid var(--color-gray-100);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-700);
}

.form-input,
.form-select,
.form-textarea {
  padding: 1rem 1.25rem;
  border: 2px solid var(--color-gray-200);
  border-radius: 12px;
  font-size: var(--font-size-base);
  background: white;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background: var(--color-gray-100);
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit-btn {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.form-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.form-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Map Section */
.map-section {
  padding: 0;
  background: var(--color-gray-100);
}

.map-container {
  max-width: 100%;
  margin: 0 auto;
}

.map-placeholder {
  height: 400px;
  background: linear-gradient(135deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.map-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="map-pattern" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="0.5" fill="rgba(0,0,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23map-pattern)"/></svg>');
  opacity: 0.3;
}

.map-content {
  text-align: center;
  color: var(--color-gray-700);
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.map-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
}

.map-icon svg {
  width: 28px;
  height: 28px;
}

.map-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.map-content p {
  color: var(--color-gray-600);
  margin-bottom: 1rem;
}

.map-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary-600);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.map-link:hover {
  color: var(--color-primary-700);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-form-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-info-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .contact-hero-section {
    height: 60vh;
    margin-top: 70px;
  }

  .contact-hero-content {
    padding: 1.5rem;
  }

  .contact-hero-title {
    font-size: clamp(2rem, 5vw, 2.5rem);
  }

  .contact-info-section,
  .contact-form-section {
    padding: 4rem 1.5rem;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-info-card {
    padding: 2rem 1.5rem;
  }

  .contact-info-icon {
    width: 70px;
    height: 70px;
  }

  .contact-info-icon svg {
    width: 28px;
    height: 28px;
  }

  .contact-form-card {
    padding: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-features {
    gap: 1rem;
  }

  .feature-icon {
    width: 45px;
    height: 45px;
  }

  .feature-icon svg {
    width: 20px;
    height: 20px;
  }

  .map-placeholder {
    height: 300px;
  }

  .map-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-hero-content {
    padding: 1rem;
  }

  .contact-hero-badge {
    font-size: var(--font-size-xs);
    padding: 0.375rem 0.75rem;
  }

  .contact-info-section,
  .contact-form-section {
    padding: 3rem 1rem;
  }

  .contact-info-card,
  .contact-form-card {
    padding: 1.5rem;
  }

  .contact-features {
    gap: 1rem;
  }

  .contact-feature {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .map-content {
    padding: 1rem;
  }
}