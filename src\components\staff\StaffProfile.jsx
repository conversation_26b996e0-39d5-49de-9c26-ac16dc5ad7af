import React, { useState, useEffect } from 'react';
import staffService, { STAFF_STATUS, STAFF_ROLES, SKILL_LEVELS, COMMISSION_TYPES } from '../../services/staffService';
import serviceService from '../../services/serviceService';
import toast from 'react-hot-toast';
import './StaffProfile.css';

const StaffProfile = ({ staff, onBack, onUpdate, onDelete, canEdit }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [performance, setPerformance] = useState(null);
  const [services, setServices] = useState([]);

  useEffect(() => {
    if (staff) {
      setFormData({
        firstName: staff.firstName || '',
        lastName: staff.lastName || '',
        email: staff.email || '',
        phone: staff.phone || '',
        role: staff.role || STAFF_ROLES.STYLIST,
        status: staff.status || STAFF_STATUS.ACTIVE,
        hireDate: staff.hireDate || '',
        dateOfBirth: staff.dateOfBirth || '',
        address: staff.address || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        hourlyRate: staff.hourlyRate || 0,
        commission: staff.commission || {
          type: COMMISSION_TYPES.PERCENTAGE,
          rate: 0,
          minimumSales: 0
        },
        specialties: staff.specialties || [],
        skills: staff.skills || [],
        notes: staff.notes || ''
      });

      // Load performance data
      const performanceData = staffService.getStaffPerformance(staff.id);
      setPerformance(performanceData);
    }

    // Load services for skills mapping
    const servicesData = serviceService.getAllServices();
    setServices(servicesData);
  }, [staff]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (formData.hourlyRate < 0) {
      newErrors.hourlyRate = 'Hourly rate cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'number' ? parseFloat(value) || 0 : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) || 0 : value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSpecialtyToggle = (specialty) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties.includes(specialty)
        ? prev.specialties.filter(s => s !== specialty)
        : [...prev.specialties, specialty]
    }));
  };

  const handleSkillChange = (serviceId, level) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.map(skill => 
        skill.serviceId === serviceId 
          ? { ...skill, level }
          : skill
      ).concat(
        prev.skills.find(skill => skill.serviceId === serviceId) 
          ? [] 
          : [{ serviceId, level }]
      )
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onUpdate(staff.id, formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating staff:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status, compact = false) => {
    const statusConfig = {
      active: {
        label: 'Active',
        className: 'status-active',
        icon: '✅',
        color: '#10b981'
      },
      inactive: {
        label: 'Inactive',
        className: 'status-inactive',
        icon: '⏸️',
        color: '#6b7280'
      },
      on_leave: {
        label: 'On Leave',
        className: 'status-leave',
        icon: '🏖️',
        color: '#f59e0b'
      },
      terminated: {
        label: 'Terminated',
        className: 'status-terminated',
        icon: '❌',
        color: '#ef4444'
      }
    };

    const config = statusConfig[status] || statusConfig.active;

    if (compact) {
      return (
        <span className={`status-badge status-compact ${config.className}`}>
          <span className="status-indicator" style={{ backgroundColor: config.color }}></span>
        </span>
      );
    }

    return (
      <span className={`status-badge ${config.className}`}>
        <span className="status-icon">{config.icon}</span>
        <span className="status-label">{config.label}</span>
        <span className="status-indicator" style={{ backgroundColor: config.color }}></span>
      </span>
    );
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      stylist: { label: 'Stylist', icon: '💇‍♀️' },
      beautician: { label: 'Beautician', icon: '✨' },
      nail_technician: { label: 'Nail Technician', icon: '💅' },
      massage_therapist: { label: 'Massage Therapist', icon: '🧘‍♀️' },
      receptionist: { label: 'Receptionist', icon: '📞' },
      manager: { label: 'Manager', icon: '👔' }
    };

    const config = roleConfig[role] || { label: role, icon: '👤' };
    return (
      <span className="role-badge">
        <span className="role-icon">{config.icon}</span>
        {config.label}
      </span>
    );
  };

  const getSkillLevelBadge = (level) => {
    const levelConfig = {
      beginner: { label: 'Beginner', className: 'skill-beginner' },
      intermediate: { label: 'Intermediate', className: 'skill-intermediate' },
      advanced: { label: 'Advanced', className: 'skill-advanced' },
      expert: { label: 'Expert', className: 'skill-expert' }
    };

    const config = levelConfig[level] || levelConfig.beginner;
    return <span className={`skill-badge ${config.className}`}>{config.label}</span>;
  };

  const calculateYearsOfService = () => {
    const hireDate = new Date(staff.hireDate);
    const today = new Date();
    const years = (today - hireDate) / (1000 * 60 * 60 * 24 * 365.25);
    return Math.floor(years * 10) / 10; // Round to 1 decimal place
  };

  const calculateAge = () => {
    if (!staff.dateOfBirth) return null;
    const birthDate = new Date(staff.dateOfBirth);
    const today = new Date();
    const age = (today - birthDate) / (1000 * 60 * 60 * 24 * 365.25);
    return Math.floor(age);
  };

  return (
    <div className="staff-profile">
      {/* Navigation Header */}
      <div className="profile-navigation">
        <button
          className="btn btn-ghost"
          onClick={onBack}
        >
          <span className="btn-icon">←</span>
          Back to Staff
        </button>

        <div className="nav-actions">
          {canEdit && !isEditing && (
            <>
              <button
                className="btn btn-outline"
                onClick={() => setIsEditing(true)}
              >
                <span className="btn-icon">✏️</span>
                Edit
              </button>
              <button
                className="btn btn-danger-outline"
                onClick={() => onDelete(staff.id)}
              >
                <span className="btn-icon">🗑️</span>
                Delete
              </button>
            </>
          )}

          {isEditing && (
            <>
              <button
                className="btn btn-ghost"
                onClick={() => setIsEditing(false)}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="btn-spinner"></span>
                    Saving...
                  </>
                ) : (
                  <>
                    <span className="btn-icon">💾</span>
                    Save
                  </>
                )}
              </button>
            </>
          )}
        </div>
      </div>

      {/* Profile Content */}
      <div className="profile-content">
        {isEditing ? (
          <form onSubmit={handleSubmit} className="profile-form">
            <div className="form-grid">
              {/* Personal Information */}
              <div className="form-section">
                <h3 className="section-title">Personal Information</h3>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="firstName" className="form-label">First Name *</label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className={`form-input ${errors.firstName ? 'form-input-error' : ''}`}
                    />
                    {errors.firstName && <span className="error-message">{errors.firstName}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="lastName" className="form-label">Last Name *</label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className={`form-input ${errors.lastName ? 'form-input-error' : ''}`}
                    />
                    {errors.lastName && <span className="error-message">{errors.lastName}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="email" className="form-label">Email *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`form-input ${errors.email ? 'form-input-error' : ''}`}
                    />
                    {errors.email && <span className="error-message">{errors.email}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="phone" className="form-label">Phone *</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`form-input ${errors.phone ? 'form-input-error' : ''}`}
                    />
                    {errors.phone && <span className="error-message">{errors.phone}</span>}
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="dateOfBirth" className="form-label">Date of Birth</label>
                    <input
                      type="date"
                      id="dateOfBirth"
                      name="dateOfBirth"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                      className="form-input"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="hireDate" className="form-label">Hire Date</label>
                    <input
                      type="date"
                      id="hireDate"
                      name="hireDate"
                      value={formData.hireDate}
                      onChange={handleInputChange}
                      className="form-input"
                    />
                  </div>
                </div>
              </div>

              {/* Employment Information */}
              <div className="form-section">
                <h3 className="section-title">Employment Information</h3>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="role" className="form-label">Role</label>
                    <select
                      id="role"
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      className="form-select"
                    >
                      {Object.entries(STAFF_ROLES).map(([key, value]) => (
                        <option key={key} value={value}>
                          {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="status" className="form-label">Status</label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="form-select"
                    >
                      {Object.entries(STAFF_STATUS).map(([key, value]) => (
                        <option key={key} value={value}>
                          {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="hourlyRate" className="form-label">Hourly Rate ($)</label>
                    <input
                      type="number"
                      id="hourlyRate"
                      name="hourlyRate"
                      value={formData.hourlyRate}
                      onChange={handleInputChange}
                      className={`form-input ${errors.hourlyRate ? 'form-input-error' : ''}`}
                      min="0"
                      step="0.01"
                    />
                    {errors.hourlyRate && <span className="error-message">{errors.hourlyRate}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="commission.type" className="form-label">Commission Type</label>
                    <select
                      id="commission.type"
                      name="commission.type"
                      value={formData.commission.type}
                      onChange={handleInputChange}
                      className="form-select"
                    >
                      {Object.entries(COMMISSION_TYPES).map(([key, value]) => (
                        <option key={key} value={value}>
                          {value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="commission.rate" className="form-label">
                      Commission Rate {formData.commission.type === 'percentage' ? '(%)' : '($)'}
                    </label>
                    <input
                      type="number"
                      id="commission.rate"
                      name="commission.rate"
                      value={formData.commission.rate}
                      onChange={handleInputChange}
                      className="form-input"
                      min="0"
                      step={formData.commission.type === 'percentage' ? '1' : '0.01'}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="commission.minimumSales" className="form-label">Minimum Sales ($)</label>
                    <input
                      type="number"
                      id="commission.minimumSales"
                      name="commission.minimumSales"
                      value={formData.commission.minimumSales}
                      onChange={handleInputChange}
                      className="form-input"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="form-section">
                <h3 className="section-title">Notes</h3>
                <div className="form-group">
                  <textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    className="form-textarea"
                    placeholder="Additional notes about this staff member..."
                    rows="4"
                  />
                </div>
              </div>
            </div>
          </form>
        ) : (
          <div className="profile-view">
            {/* Hero Section */}
            <div className="profile-hero">
              <div className="hero-background"></div>
              <div className="hero-content">
                <div className="staff-avatar-hero">
                  {staff.avatar ? (
                    <img src={staff.avatar} alt={`${staff.firstName} ${staff.lastName}`} />
                  ) : (
                    <div className="avatar-placeholder-hero">
                      {staff.firstName[0]}{staff.lastName[0]}
                    </div>
                  )}
                  <div className="avatar-status">
                    {getStatusBadge(staff.status, true)}
                  </div>
                </div>

                <div className="hero-info">
                  <h1 className="staff-name-hero">{staff.firstName} {staff.lastName}</h1>
                  <div className="staff-role-hero">
                    {getRoleBadge(staff.role)}
                  </div>

                  <div className="hero-stats">
                    <div className="hero-stat">
                      <span className="stat-icon">📅</span>
                      <div className="stat-content">
                        <span className="stat-value">{calculateYearsOfService()}</span>
                        <span className="stat-label">Years of Service</span>
                      </div>
                    </div>

                    {calculateAge() && (
                      <div className="hero-stat">
                        <span className="stat-icon">🎂</span>
                        <div className="stat-content">
                          <span className="stat-value">{calculateAge()}</span>
                          <span className="stat-label">Years Old</span>
                        </div>
                      </div>
                    )}

                    <div className="hero-stat">
                      <span className="stat-icon">💰</span>
                      <div className="stat-content">
                        <span className="stat-value">${staff.hourlyRate}</span>
                        <span className="stat-label">Hourly Rate</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Information Cards */}
            <div className="profile-cards">
              <div className="info-card">
                <div className="card-header">
                  <span className="card-icon">📧</span>
                  <h3 className="card-title">Contact Information</h3>
                </div>
                <div className="card-content">
                  <div className="info-item">
                    <span className="info-icon">✉️</span>
                    <div className="info-details">
                      <span className="info-label">Email</span>
                      <span className="info-value">{staff.email}</span>
                    </div>
                  </div>
                  <div className="info-item">
                    <span className="info-icon">📱</span>
                    <div className="info-details">
                      <span className="info-label">Phone</span>
                      <span className="info-value">{staff.phone}</span>
                    </div>
                  </div>
                  {staff.address && (
                    <div className="info-item">
                      <span className="info-icon">📍</span>
                      <div className="info-details">
                        <span className="info-label">Address</span>
                        <span className="info-value">
                          {staff.address.street}, {staff.address.city}, {staff.address.state} {staff.address.zipCode}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="info-card">
                <div className="card-header">
                  <span className="card-icon">💼</span>
                  <h3 className="card-title">Employment Details</h3>
                </div>
                <div className="card-content">
                  <div className="info-item">
                    <span className="info-icon">📅</span>
                    <div className="info-details">
                      <span className="info-label">Hire Date</span>
                      <span className="info-value">
                        {new Date(staff.hireDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="info-item">
                    <span className="info-icon">💰</span>
                    <div className="info-details">
                      <span className="info-label">Commission</span>
                      <span className="info-value">
                        {staff.commission.type === 'percentage'
                          ? `${staff.commission.rate}%`
                          : `$${staff.commission.rate}`
                        } (Min: ${staff.commission.minimumSales})
                      </span>
                    </div>
                  </div>
                  {staff.dateOfBirth && (
                    <div className="info-item">
                      <span className="info-icon">🎂</span>
                      <div className="info-details">
                        <span className="info-label">Date of Birth</span>
                        <span className="info-value">
                          {new Date(staff.dateOfBirth).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {staff.specialties && staff.specialties.length > 0 && (
                <div className="info-card">
                  <div className="card-header">
                    <span className="card-icon">⭐</span>
                    <h3 className="card-title">Specialties</h3>
                  </div>
                  <div className="card-content">
                    <div className="specialty-grid">
                      {staff.specialties.map((specialty, index) => (
                        <div key={index} className="specialty-item">
                          <span className="specialty-icon">✨</span>
                          <span className="specialty-name">{specialty}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {staff.skills && staff.skills.length > 0 && (
                <div className="info-card">
                  <div className="card-header">
                    <span className="card-icon">🎯</span>
                    <h3 className="card-title">Skills & Expertise</h3>
                  </div>
                  <div className="card-content">
                    <div className="skills-grid">
                      {staff.skills.map((skill, index) => {
                        const service = services.find(s => s.id === skill.serviceId);
                        return (
                          <div key={index} className="skill-item">
                            <div className="skill-info">
                              <span className="skill-name">
                                {service ? service.name : skill.serviceId}
                              </span>
                              {getSkillLevelBadge(skill.level)}
                            </div>
                            <div className="skill-progress">
                              <div className={`progress-bar progress-${skill.level.toLowerCase()}`}></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}

              {performance && (
                <div className="info-card performance-card">
                  <div className="card-header">
                    <span className="card-icon">📊</span>
                    <h3 className="card-title">Performance Overview</h3>
                  </div>
                  <div className="card-content">
                    <div className="performance-grid">
                      <div className="performance-metric">
                        <div className="metric-icon">📅</div>
                        <div className="metric-content">
                          <span className="metric-value">{performance.monthlyStats.appointmentsCompleted}</span>
                          <span className="metric-label">Monthly Appointments</span>
                        </div>
                      </div>
                      <div className="performance-metric">
                        <div className="metric-icon">💰</div>
                        <div className="metric-content">
                          <span className="metric-value">${performance.monthlyStats.totalRevenue}</span>
                          <span className="metric-label">Monthly Revenue</span>
                        </div>
                      </div>
                      <div className="performance-metric">
                        <div className="metric-icon">⭐</div>
                        <div className="metric-content">
                          <span className="metric-value">{performance.monthlyStats.averageRating}/5.0</span>
                          <span className="metric-label">Average Rating</span>
                        </div>
                      </div>
                      <div className="performance-metric">
                        <div className="metric-icon">💎</div>
                        <div className="metric-content">
                          <span className="metric-value">${performance.monthlyStats.commissionEarned}</span>
                          <span className="metric-label">Commission Earned</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {staff.notes && (
                <div className="info-card notes-card">
                  <div className="card-header">
                    <span className="card-icon">📝</span>
                    <h3 className="card-title">Notes</h3>
                  </div>
                  <div className="card-content">
                    <div className="notes-content">{staff.notes}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StaffProfile;
