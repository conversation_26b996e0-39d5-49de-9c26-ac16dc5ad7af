import React, { useState, useEffect } from 'react';
import customerService from '../../services/customerService';
import toast from 'react-hot-toast';
import './CustomerForm.css';

const CustomerForm = ({ customer, onSave, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    preferences: {
      preferredServices: [],
      preferredStaff: [],
      allergies: '',
      notes: ''
    }
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // Available services and staff (in a real app, these would come from the backend)
  const availableServices = [
    'Haircut', 'Hair Color', 'Highlights', 'Perm', 'Facial', 
    'Manicure', 'Pedicure', 'Massage', 'Eyebrow Threading', 'Hair Styling'
  ];

  const availableStaff = [
    'Staff 1', 'Staff 2', 'Staff 3', 'Staff 4', 'Staff 5'
  ];

  // Initialize form data when customer prop changes
  useEffect(() => {
    if (customer && isEditing) {
      setFormData({
        firstName: customer.firstName || '',
        lastName: customer.lastName || '',
        email: customer.email || '',
        phone: customer.phone || '',
        dateOfBirth: customer.dateOfBirth || '',
        address: {
          street: customer.address?.street || '',
          city: customer.address?.city || '',
          state: customer.address?.state || '',
          zipCode: customer.address?.zipCode || ''
        },
        preferences: {
          preferredServices: customer.preferences?.preferredServices || [],
          preferredStaff: customer.preferences?.preferredStaff || [],
          allergies: customer.preferences?.allergies || '',
          notes: customer.preferences?.notes || ''
        }
      });
    }
  }, [customer, isEditing]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle checkbox changes for services and staff
  const handleCheckboxChange = (type, value) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [type]: prev.preferences[type].includes(value)
          ? prev.preferences[type].filter(item => item !== value)
          : [...prev.preferences[type], value]
      }
    }));
  };

  // Validate form
  const validateForm = () => {
    const validation = customerService.validateCustomer(formData);
    
    // Additional validation for email uniqueness
    if (validation.isValid && formData.email) {
      const emailExists = customerService.isEmailExists(
        formData.email, 
        isEditing ? customer.id : null
      );
      
      if (emailExists) {
        validation.isValid = false;
        validation.errors.email = 'This email address is already registered';
      }
    }

    setErrors(validation.errors);
    return validation.isValid;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);
    
    try {
      let result;
      
      if (isEditing) {
        result = customerService.updateCustomer(customer.id, formData);
      } else {
        result = customerService.addCustomer(formData);
      }

      if (result.success) {
        toast.success(
          isEditing 
            ? 'Customer updated successfully!' 
            : 'Customer added successfully!'
        );
        onSave(result.customer);
      } else {
        toast.error(result.error || 'An error occurred');
      }
    } catch (error) {
      console.error('Error saving customer:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="customer-form-container">
      <div className="customer-form-header">
        <h2>{isEditing ? 'Edit Customer' : 'Add New Customer'}</h2>
        <button onClick={onCancel} className="close-btn">✕</button>
      </div>

      <form onSubmit={handleSubmit} className="customer-form">
        {/* Personal Information */}
        <div className="form-section">
          <h3>Personal Information</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="firstName">First Name *</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className={errors.firstName ? 'error' : ''}
                placeholder="Enter first name"
              />
              {errors.firstName && <span className="error-message">{errors.firstName}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="lastName">Last Name *</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className={errors.lastName ? 'error' : ''}
                placeholder="Enter last name"
              />
              {errors.lastName && <span className="error-message">{errors.lastName}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={errors.email ? 'error' : ''}
                placeholder="Enter email address"
              />
              {errors.email && <span className="error-message">{errors.email}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="phone">Phone *</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={errors.phone ? 'error' : ''}
                placeholder="Enter phone number"
              />
              {errors.phone && <span className="error-message">{errors.phone}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="dateOfBirth">Date of Birth</label>
              <input
                type="date"
                id="dateOfBirth"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="form-section">
          <h3>Address Information</h3>
          
          <div className="form-row">
            <div className="form-group full-width">
              <label htmlFor="address.street">Street Address</label>
              <input
                type="text"
                id="address.street"
                name="address.street"
                value={formData.address.street}
                onChange={handleInputChange}
                placeholder="Enter street address"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="address.city">City</label>
              <input
                type="text"
                id="address.city"
                name="address.city"
                value={formData.address.city}
                onChange={handleInputChange}
                placeholder="Enter city"
              />
            </div>

            <div className="form-group">
              <label htmlFor="address.state">State</label>
              <input
                type="text"
                id="address.state"
                name="address.state"
                value={formData.address.state}
                onChange={handleInputChange}
                placeholder="Enter state"
              />
            </div>

            <div className="form-group">
              <label htmlFor="address.zipCode">ZIP Code</label>
              <input
                type="text"
                id="address.zipCode"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleInputChange}
                placeholder="Enter ZIP code"
              />
            </div>
          </div>
        </div>

        {/* Preferences */}
        <div className="form-section">
          <h3>Preferences</h3>
          
          <div className="form-group">
            <label>Preferred Services</label>
            <div className="checkbox-group">
              {availableServices.map(service => (
                <label key={service} className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.preferences.preferredServices.includes(service)}
                    onChange={() => handleCheckboxChange('preferredServices', service)}
                  />
                  <span>{service}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="form-group">
            <label>Preferred Staff</label>
            <div className="checkbox-group">
              {availableStaff.map(staff => (
                <label key={staff} className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.preferences.preferredStaff.includes(staff)}
                    onChange={() => handleCheckboxChange('preferredStaff', staff)}
                  />
                  <span>{staff}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="preferences.allergies">Allergies</label>
              <input
                type="text"
                id="preferences.allergies"
                name="preferences.allergies"
                value={formData.preferences.allergies}
                onChange={handleInputChange}
                placeholder="Enter any allergies"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group full-width">
              <label htmlFor="preferences.notes">Notes</label>
              <textarea
                id="preferences.notes"
                name="preferences.notes"
                value={formData.preferences.notes}
                onChange={handleInputChange}
                placeholder="Enter any additional notes"
                rows="3"
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : (isEditing ? 'Update Customer' : 'Add Customer')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CustomerForm;
